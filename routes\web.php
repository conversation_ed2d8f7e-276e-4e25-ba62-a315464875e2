<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Calculator & Simulation Routes
Route::get('ban-27-bundling', function () {
    return Inertia::render('calculators/Ban27BundlingCalculator');
})->middleware(['auth', 'verified'])->name('ban-27-bundling');

Route::get('bundling-calculator', function () {
    return Inertia::render('calculators/BundlingQtyCalculator');
})->middleware(['auth', 'verified'])->name('bundling-calculator');

Route::get('zero-margin-bundling', function () {
    return Inertia::render('calculators/ZeroMarginBundlingCalculator');
})->middleware(['auth', 'verified'])->name('zero-margin-bundling');

Route::get('promo-simulation', function () {
    return Inertia::render('calculators/PromoSimulationPage');
})->middleware(['auth', 'verified'])->name('promo-simulation');

// Data Management Routes
Route::get('products', function () {
    return Inertia::render('data-management/ProductManagement');
})->middleware(['auth', 'verified'])->name('products');

Route::get('customers', function () {
    return Inertia::render('data-management/CustomerPage');
})->middleware(['auth', 'verified'])->name('customers');

Route::get('coal-price-data', function () {
    return Inertia::render('data-management/CoalPriceDataPage');
})->middleware(['auth', 'verified'])->name('coal-price-data');

Route::get('template-management', function () {
    return Inertia::render('data-management/TemplateManagement');
})->middleware(['auth', 'verified'])->name('template-management');

Route::get('sales-revenue-2025-data-master', function () {
    return Inertia::render('data-management/SalesRevenue2025DataMaster');
})->middleware(['auth', 'verified'])->name('sales-revenue-2025-data-master');

Route::get('proposal-builder', function () {
    return Inertia::render('data-management/ProposalBuilder');
})->middleware(['auth', 'verified'])->name('proposal-builder');

// Analysis & Dashboard Routes
Route::get('analytics-dashboard', function () {
    return Inertia::render('analytics/AnalyticsDashboard');
})->middleware(['auth', 'verified'])->name('analytics-dashboard');

Route::get('sales-dashboard', function () {
    return Inertia::render('analytics/SalesDashboard');
})->middleware(['auth', 'verified'])->name('sales-dashboard');

Route::get('fleet-analyzer', function () {
    return Inertia::render('analytics/FleetAnalyzer');
})->middleware(['auth', 'verified'])->name('fleet-analyzer');

Route::get('customer-analysis', function () {
    return Inertia::render('analytics/CustomerAnalysis');
})->middleware(['auth', 'verified'])->name('customer-analysis');

Route::get('product-analysis', function () {
    return Inertia::render('analytics/ProductAnalysis');
})->middleware(['auth', 'verified'])->name('product-analysis');

// Marketing & Proposal Routes
Route::get('seasonal-marketing-calendar', function () {
    return Inertia::render('marketing/SeasonalMarketingCalendar');
})->middleware(['auth', 'verified'])->name('seasonal-marketing-calendar');

Route::get('bundling-proposal', function () {
    return Inertia::render('marketing/BundlingProposal');
})->middleware(['auth', 'verified'])->name('bundling-proposal');

Route::get('marketing-insights', function () {
    return Inertia::render('marketing/MarketingInsightsHub');
})->middleware(['auth', 'verified'])->name('marketing-insights');

Route::get('content-planning-tools', function () {
    return Inertia::render('marketing/ContentPlanningTools');
})->middleware(['auth', 'verified'])->name('content-planning-tools');

Route::get('negotiation-simulator', function () {
    return Inertia::render('ai-tools/NegotiationSimulator');
})->middleware(['auth', 'verified'])->name('negotiation-simulator');

// Additional Data Management Routes
Route::get('sales-revenue-2025-data-master', function () {
    return Inertia::render('data-management/SalesRevenue2025DataMaster');
})->middleware(['auth', 'verified'])->name('sales-revenue-2025-data-master');

Route::get('proposal-builder', function () {
    return Inertia::render('data-management/ProposalBuilder');
})->middleware(['auth', 'verified'])->name('proposal-builder');

// Additional Analytics Routes
Route::get('fleet-analyzer', function () {
    return Inertia::render('analytics/FleetAnalyzer');
})->middleware(['auth', 'verified'])->name('fleet-analyzer');

Route::get('customer-analysis', function () {
    return Inertia::render('analytics/CustomerAnalysis');
})->middleware(['auth', 'verified'])->name('customer-analysis');

Route::get('product-analysis', function () {
    return Inertia::render('analytics/ProductAnalysis');
})->middleware(['auth', 'verified'])->name('product-analysis');

// Social Media Marketing Routes
Route::get('social-media-marketing', function () {
    return Inertia::render('social-media/SocialMediaMarketing');
})->middleware(['auth', 'verified'])->name('social-media-marketing');

Route::get('monthly-content-plan', function () {
    return Inertia::render('social-media/MonthlyContentPlan');
})->middleware(['auth', 'verified'])->name('monthly-content-plan');

Route::get('video-script-generator', function () {
    return Inertia::render('social-media/VideoScriptGenerator');
})->middleware(['auth', 'verified'])->name('video-script-generator');

Route::get('instagram-analysis', function () {
    return Inertia::render('social-media/InstagramAnalysis');
})->middleware(['auth', 'verified'])->name('instagram-analysis');

// AI Tools Routes
Route::get('negotiation-simulator', function () {
    return Inertia::render('ai-tools/NegotiationSimulator');
})->middleware(['auth', 'verified'])->name('negotiation-simulator');

Route::get('whatsapp-chat-analysis', function () {
    return Inertia::render('ai-tools/WhatsAppChatAnalysis');
})->middleware(['auth', 'verified'])->name('whatsapp-chat-analysis');

Route::get('knowledge-base', function () {
    return Inertia::render('ai-tools/KnowledgeBase');
})->middleware(['auth', 'verified'])->name('knowledge-base');

Route::get('proposal-analyzer', function () {
    return Inertia::render('ai-tools/ProposalAnalyzer');
})->middleware(['auth', 'verified'])->name('proposal-analyzer');

Route::get('image-generator', function () {
    return Inertia::render('ai-tools/ImageGenerator');
})->middleware(['auth', 'verified'])->name('image-generator');

Route::get('swot-analysis', function () {
    return Inertia::render('ai-tools/SwotAnalysis');
})->middleware(['auth', 'verified'])->name('swot-analysis');

Route::get('presentation-analyzer', function () {
    return Inertia::render('ai-tools/PresentationAnalyzer');
})->middleware(['auth', 'verified'])->name('presentation-analyzer');

// Style Guide Route
Route::get('style-guide', function () {
    return Inertia::render('StyleGuide');
})->middleware(['auth', 'verified'])->name('style-guide');

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
