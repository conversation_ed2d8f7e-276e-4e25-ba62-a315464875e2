<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::get('product-management', function () {
    return Inertia::render('ProductManagement');
})->middleware(['auth', 'verified'])->name('product-management');

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
