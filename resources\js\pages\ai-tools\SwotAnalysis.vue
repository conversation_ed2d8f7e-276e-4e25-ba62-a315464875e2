<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">AI SWOT Analysis</h1>
                    <p class="mt-2 text-gray-600">AI-powered SWOT analysis for strategic business planning and decision making</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="generateAIAnalysis"
                        class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center"
                    >
                        <Brain class="h-4 w-4 mr-2" />
                        AI Analysis
                    </button>
                    <button
                        @click="exportSWOT"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export SWOT
                    </button>
                </div>
            </div>

            <!-- Analysis Setup -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Analysis Configuration</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Analysis Subject</label>
                        <input 
                            v-model="analysisConfig.subject" 
                            type="text" 
                            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="e.g., Chitra Tire Company, New Product Launch, Market Expansion"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Analysis Type</label>
                        <select 
                            v-model="analysisConfig.type" 
                            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="company">Company Analysis</option>
                            <option value="product">Product Analysis</option>
                            <option value="market">Market Analysis</option>
                            <option value="project">Project Analysis</option>
                            <option value="strategy">Strategy Analysis</option>
                        </select>
                    </div>
                </div>

                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Context & Background</label>
                    <textarea 
                        v-model="analysisConfig.context" 
                        rows="3"
                        class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Provide context about the subject, current situation, goals, and any specific areas of focus..."
                    ></textarea>
                </div>

                <div class="mt-6 flex justify-center">
                    <button
                        @click="startAnalysis"
                        :disabled="!analysisConfig.subject.trim()"
                        class="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                        <Target class="h-5 w-5 mr-2" />
                        Start SWOT Analysis
                    </button>
                </div>
            </div>

            <!-- SWOT Matrix -->
            <div v-if="swotData" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Strengths -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 bg-green-50 border-b border-green-200 rounded-t-lg">
                        <div class="flex items-center">
                            <TrendingUp class="h-6 w-6 text-green-600 mr-3" />
                            <h3 class="text-lg font-semibold text-green-900">Strengths</h3>
                            <span class="ml-auto bg-green-100 text-green-800 text-sm font-medium px-2 py-1 rounded">
                                {{ swotData.strengths.length }}
                            </span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <div v-for="(strength, index) in swotData.strengths" :key="index" class="flex items-start">
                                <div class="flex-shrink-0 w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium mt-0.5">
                                    {{ index + 1 }}
                                </div>
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-gray-900">{{ strength.title }}</p>
                                    <p class="text-sm text-gray-600 mt-1">{{ strength.description }}</p>
                                    <div class="flex items-center mt-2">
                                        <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">
                                            Impact: {{ strength.impact }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button
                            @click="addItem('strengths')"
                            class="mt-4 w-full px-3 py-2 border border-green-300 text-green-700 rounded-md hover:bg-green-50 flex items-center justify-center"
                        >
                            <Plus class="h-4 w-4 mr-2" />
                            Add Strength
                        </button>
                    </div>
                </div>

                <!-- Weaknesses -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 bg-red-50 border-b border-red-200 rounded-t-lg">
                        <div class="flex items-center">
                            <TrendingDown class="h-6 w-6 text-red-600 mr-3" />
                            <h3 class="text-lg font-semibold text-red-900">Weaknesses</h3>
                            <span class="ml-auto bg-red-100 text-red-800 text-sm font-medium px-2 py-1 rounded">
                                {{ swotData.weaknesses.length }}
                            </span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <div v-for="(weakness, index) in swotData.weaknesses" :key="index" class="flex items-start">
                                <div class="flex-shrink-0 w-6 h-6 bg-red-100 text-red-600 rounded-full flex items-center justify-center text-sm font-medium mt-0.5">
                                    {{ index + 1 }}
                                </div>
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-gray-900">{{ weakness.title }}</p>
                                    <p class="text-sm text-gray-600 mt-1">{{ weakness.description }}</p>
                                    <div class="flex items-center mt-2">
                                        <span class="text-xs text-red-600 bg-red-100 px-2 py-1 rounded">
                                            Priority: {{ weakness.priority }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button
                            @click="addItem('weaknesses')"
                            class="mt-4 w-full px-3 py-2 border border-red-300 text-red-700 rounded-md hover:bg-red-50 flex items-center justify-center"
                        >
                            <Plus class="h-4 w-4 mr-2" />
                            Add Weakness
                        </button>
                    </div>
                </div>

                <!-- Opportunities -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 bg-blue-50 border-b border-blue-200 rounded-t-lg">
                        <div class="flex items-center">
                            <Lightbulb class="h-6 w-6 text-blue-600 mr-3" />
                            <h3 class="text-lg font-semibold text-blue-900">Opportunities</h3>
                            <span class="ml-auto bg-blue-100 text-blue-800 text-sm font-medium px-2 py-1 rounded">
                                {{ swotData.opportunities.length }}
                            </span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <div v-for="(opportunity, index) in swotData.opportunities" :key="index" class="flex items-start">
                                <div class="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mt-0.5">
                                    {{ index + 1 }}
                                </div>
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-gray-900">{{ opportunity.title }}</p>
                                    <p class="text-sm text-gray-600 mt-1">{{ opportunity.description }}</p>
                                    <div class="flex items-center mt-2">
                                        <span class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                                            Potential: {{ opportunity.potential }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button
                            @click="addItem('opportunities')"
                            class="mt-4 w-full px-3 py-2 border border-blue-300 text-blue-700 rounded-md hover:bg-blue-50 flex items-center justify-center"
                        >
                            <Plus class="h-4 w-4 mr-2" />
                            Add Opportunity
                        </button>
                    </div>
                </div>

                <!-- Threats -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 bg-orange-50 border-b border-orange-200 rounded-t-lg">
                        <div class="flex items-center">
                            <AlertTriangle class="h-6 w-6 text-orange-600 mr-3" />
                            <h3 class="text-lg font-semibold text-orange-900">Threats</h3>
                            <span class="ml-auto bg-orange-100 text-orange-800 text-sm font-medium px-2 py-1 rounded">
                                {{ swotData.threats.length }}
                            </span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <div v-for="(threat, index) in swotData.threats" :key="index" class="flex items-start">
                                <div class="flex-shrink-0 w-6 h-6 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-sm font-medium mt-0.5">
                                    {{ index + 1 }}
                                </div>
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-gray-900">{{ threat.title }}</p>
                                    <p class="text-sm text-gray-600 mt-1">{{ threat.description }}</p>
                                    <div class="flex items-center mt-2">
                                        <span class="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded">
                                            Risk: {{ threat.risk }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button
                            @click="addItem('threats')"
                            class="mt-4 w-full px-3 py-2 border border-orange-300 text-orange-700 rounded-md hover:bg-orange-50 flex items-center justify-center"
                        >
                            <Plus class="h-4 w-4 mr-2" />
                            Add Threat
                        </button>
                    </div>
                </div>
            </div>

            <!-- Strategic Recommendations -->
            <div v-if="swotData" class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">AI Strategic Recommendations</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">SO Strategies (Strengths + Opportunities)</h4>
                        <div v-for="strategy in strategicRecommendations.so" :key="strategy.id" class="p-3 bg-green-50 rounded-lg">
                            <p class="text-sm font-medium text-green-900">{{ strategy.title }}</p>
                            <p class="text-sm text-green-700 mt-1">{{ strategy.description }}</p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">WO Strategies (Weaknesses + Opportunities)</h4>
                        <div v-for="strategy in strategicRecommendations.wo" :key="strategy.id" class="p-3 bg-blue-50 rounded-lg">
                            <p class="text-sm font-medium text-blue-900">{{ strategy.title }}</p>
                            <p class="text-sm text-blue-700 mt-1">{{ strategy.description }}</p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">ST Strategies (Strengths + Threats)</h4>
                        <div v-for="strategy in strategicRecommendations.st" :key="strategy.id" class="p-3 bg-yellow-50 rounded-lg">
                            <p class="text-sm font-medium text-yellow-900">{{ strategy.title }}</p>
                            <p class="text-sm text-yellow-700 mt-1">{{ strategy.description }}</p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">WT Strategies (Weaknesses + Threats)</h4>
                        <div v-for="strategy in strategicRecommendations.wt" :key="strategy.id" class="p-3 bg-red-50 rounded-lg">
                            <p class="text-sm font-medium text-red-900">{{ strategy.title }}</p>
                            <p class="text-sm text-red-700 mt-1">{{ strategy.description }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-purple-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-purple-900">Advanced SWOT Analysis Features Coming Soon</h3>
                        <p class="text-purple-700 mt-1">
                            Enhanced strategic analysis features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-purple-700 mt-2 space-y-1">
                            <li>Industry-specific SWOT templates and benchmarks</li>
                            <li>Competitive SWOT analysis and comparison</li>
                            <li>Dynamic SWOT updates based on market data</li>
                            <li>Integration with business intelligence tools</li>
                            <li>Collaborative team analysis and voting</li>
                            <li>Action plan generation from SWOT insights</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Brain,
    Download,
    Target,
    TrendingUp,
    TrendingDown,
    Lightbulb,
    AlertTriangle,
    Plus,
    Info
} from 'lucide-vue-next';
import { ref, onMounted } from 'vue';

// Types
interface SWOTItem {
    title: string;
    description: string;
    impact?: string;
    priority?: string;
    potential?: string;
    risk?: string;
}

interface SWOTData {
    strengths: SWOTItem[];
    weaknesses: SWOTItem[];
    opportunities: SWOTItem[];
    threats: SWOTItem[];
}

interface StrategicRecommendation {
    id: string;
    title: string;
    description: string;
}

// Reactive state
const analysisConfig = ref({
    subject: '',
    type: 'company',
    context: ''
});

const swotData = ref<SWOTData | null>(null);

// Strategic recommendations
const strategicRecommendations = ref({
    so: [] as StrategicRecommendation[],
    wo: [] as StrategicRecommendation[],
    st: [] as StrategicRecommendation[],
    wt: [] as StrategicRecommendation[]
});

// Demo SWOT data
const demoSWOTData: SWOTData = {
    strengths: [
        {
            title: 'Strong Brand Recognition',
            description: 'Well-established brand in Indonesian tire market with 20+ years of experience',
            impact: 'High'
        },
        {
            title: 'Quality Product Portfolio',
            description: 'Comprehensive range of high-quality tires for various industrial applications',
            impact: 'High'
        },
        {
            title: 'Strategic Location',
            description: 'Optimal distribution network covering major industrial areas',
            impact: 'Medium'
        },
        {
            title: 'Technical Expertise',
            description: 'Experienced technical team with deep industry knowledge',
            impact: 'High'
        }
    ],
    weaknesses: [
        {
            title: 'Limited Digital Presence',
            description: 'Underdeveloped online marketing and e-commerce capabilities',
            priority: 'High'
        },
        {
            title: 'Dependency on Imports',
            description: 'Heavy reliance on imported raw materials affecting cost structure',
            priority: 'Medium'
        },
        {
            title: 'Limited R&D Investment',
            description: 'Insufficient investment in research and development compared to competitors',
            priority: 'High'
        }
    ],
    opportunities: [
        {
            title: 'Growing Mining Industry',
            description: 'Expansion of mining operations in Indonesia creating demand for heavy equipment tires',
            potential: 'High'
        },
        {
            title: 'Digital Transformation',
            description: 'Opportunity to leverage digital technologies for better customer engagement',
            potential: 'Medium'
        },
        {
            title: 'Sustainability Trends',
            description: 'Increasing demand for eco-friendly and sustainable tire solutions',
            potential: 'High'
        },
        {
            title: 'Government Infrastructure Projects',
            description: 'Large-scale infrastructure development creating demand for construction tires',
            potential: 'High'
        }
    ],
    threats: [
        {
            title: 'Intense Competition',
            description: 'Strong competition from international tire manufacturers',
            risk: 'High'
        },
        {
            title: 'Economic Volatility',
            description: 'Economic uncertainty affecting customer purchasing decisions',
            risk: 'Medium'
        },
        {
            title: 'Raw Material Price Fluctuation',
            description: 'Volatile prices of rubber and other raw materials impacting profitability',
            risk: 'High'
        },
        {
            title: 'Regulatory Changes',
            description: 'Potential changes in import/export regulations and environmental standards',
            risk: 'Medium'
        }
    ]
};

// Demo strategic recommendations
const demoStrategicRecommendations = {
    so: [
        {
            id: '1',
            title: 'Leverage Brand for Mining Expansion',
            description: 'Use strong brand recognition to capture growing mining industry opportunities'
        },
        {
            id: '2',
            title: 'Technical Excellence in Sustainability',
            description: 'Apply technical expertise to develop eco-friendly tire solutions'
        }
    ],
    wo: [
        {
            id: '1',
            title: 'Digital Transformation Initiative',
            description: 'Invest in digital capabilities to capitalize on digital transformation opportunities'
        },
        {
            id: '2',
            title: 'R&D for Sustainable Products',
            description: 'Increase R&D investment to develop sustainable tire technologies'
        }
    ],
    st: [
        {
            id: '1',
            title: 'Differentiation Strategy',
            description: 'Use quality and technical expertise to differentiate from competitors'
        },
        {
            id: '2',
            title: 'Strategic Partnerships',
            description: 'Form partnerships to mitigate raw material price risks'
        }
    ],
    wt: [
        {
            id: '1',
            title: 'Cost Optimization',
            description: 'Address import dependency to reduce vulnerability to economic volatility'
        },
        {
            id: '2',
            title: 'Digital Marketing Focus',
            description: 'Improve digital presence to compete more effectively'
        }
    ]
};

// Event handlers
const startAnalysis = () => {
    if (!analysisConfig.value.subject.trim()) return;

    // Load demo data
    swotData.value = demoSWOTData;
    strategicRecommendations.value = demoStrategicRecommendations;

    alert(`SWOT Analysis started for: ${analysisConfig.value.subject}\n\nAnalysis Type: ${analysisConfig.value.type}\n\nDemo data loaded. In production, this would analyze the subject using AI and market data.`);
};

const generateAIAnalysis = () => {
    if (!swotData.value) {
        alert('Please start a SWOT analysis first.');
        return;
    }

    alert('Generating AI-powered insights...\n\nAnalyzing:\n- Market trends and data\n- Competitor analysis\n- Industry benchmarks\n- Strategic recommendations\n\nEnhanced insights will be added to your SWOT matrix.');
};

const addItem = (category: keyof SWOTData) => {
    const categoryNames = {
        strengths: 'Strength',
        weaknesses: 'Weakness',
        opportunities: 'Opportunity',
        threats: 'Threat'
    };

    const title = prompt(`Enter ${categoryNames[category]} title:`);
    if (!title) return;

    const description = prompt(`Enter ${categoryNames[category]} description:`);
    if (!description) return;

    const newItem: SWOTItem = { title, description };

    // Add category-specific properties
    if (category === 'strengths') {
        const impact = prompt('Enter impact level (High/Medium/Low):') || 'Medium';
        newItem.impact = impact;
    } else if (category === 'weaknesses') {
        const priority = prompt('Enter priority level (High/Medium/Low):') || 'Medium';
        newItem.priority = priority;
    } else if (category === 'opportunities') {
        const potential = prompt('Enter potential level (High/Medium/Low):') || 'Medium';
        newItem.potential = potential;
    } else if (category === 'threats') {
        const risk = prompt('Enter risk level (High/Medium/Low):') || 'Medium';
        newItem.risk = risk;
    }

    if (swotData.value) {
        swotData.value[category].push(newItem);
    }
};

const exportSWOT = () => {
    if (!swotData.value) {
        alert('No SWOT analysis to export. Please create an analysis first.');
        return;
    }

    try {
        const csvContent = [
            ['SWOT Analysis Report'].join(','),
            ['Subject:', analysisConfig.value.subject].join(','),
            ['Type:', analysisConfig.value.type].join(','),
            ['Generated:', new Date().toLocaleDateString('id-ID')].join(','),
            [],
            ['STRENGTHS'].join(','),
            ['Title', 'Description', 'Impact'].join(','),
            ...swotData.value.strengths.map(item => [
                `"${item.title}"`,
                `"${item.description}"`,
                item.impact || ''
            ].join(',')),
            [],
            ['WEAKNESSES'].join(','),
            ['Title', 'Description', 'Priority'].join(','),
            ...swotData.value.weaknesses.map(item => [
                `"${item.title}"`,
                `"${item.description}"`,
                item.priority || ''
            ].join(',')),
            [],
            ['OPPORTUNITIES'].join(','),
            ['Title', 'Description', 'Potential'].join(','),
            ...swotData.value.opportunities.map(item => [
                `"${item.title}"`,
                `"${item.description}"`,
                item.potential || ''
            ].join(',')),
            [],
            ['THREATS'].join(','),
            ['Title', 'Description', 'Risk'].join(','),
            ...swotData.value.threats.map(item => [
                `"${item.title}"`,
                `"${item.description}"`,
                item.risk || ''
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `swot_analysis_${analysisConfig.value.subject.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('SWOT analysis exported successfully!');
    } catch (error) {
        console.error('Error exporting SWOT analysis:', error);
        alert('Failed to export SWOT analysis.');
    }
};

// Initialize on mount
onMounted(() => {
    // Load any saved analysis
    try {
        const savedAnalysis = localStorage.getItem('swot_analysis');
        if (savedAnalysis) {
            const parsed = JSON.parse(savedAnalysis);
            analysisConfig.value = parsed.config || analysisConfig.value;
            swotData.value = parsed.data || null;
            strategicRecommendations.value = parsed.recommendations || strategicRecommendations.value;
        }
    } catch (error) {
        console.error('Error loading saved analysis:', error);
    }
});

// Save analysis when data changes
import { watch } from 'vue';
watch([swotData, analysisConfig, strategicRecommendations], () => {
    try {
        const analysisToSave = {
            config: analysisConfig.value,
            data: swotData.value,
            recommendations: strategicRecommendations.value,
            savedAt: new Date().toISOString()
        };
        localStorage.setItem('swot_analysis', JSON.stringify(analysisToSave));
    } catch (error) {
        console.error('Error saving analysis:', error);
    }
}, { deep: true });
</script>
