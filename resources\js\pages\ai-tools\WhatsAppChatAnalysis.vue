<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">WhatsApp Chat Analysis</h1>
                    <p class="mt-2 text-gray-600">AI-powered analysis of WhatsApp business conversations for insights and optimization</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="uploadChat"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Upload class="h-4 w-4 mr-2" />
                        Upload Chat
                    </button>
                    <button
                        @click="exportAnalysis"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export Analysis
                    </button>
                </div>
            </div>

            <!-- Upload Section -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Upload WhatsApp Chat</h3>
                
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <MessageSquare class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h4 class="text-lg font-medium text-gray-900 mb-2">Drop your WhatsApp chat export here</h4>
                    <p class="text-gray-600 mb-4">
                        Export your WhatsApp chat as a .txt file and upload it for AI analysis
                    </p>
                    <div class="flex justify-center space-x-4">
                        <button
                            @click="selectFile"
                            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                        >
                            Select File
                        </button>
                        <button
                            @click="showDemo"
                            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                        >
                            View Demo Analysis
                        </button>
                    </div>
                </div>

                <div class="mt-4 text-sm text-gray-500">
                    <p><strong>How to export WhatsApp chat:</strong></p>
                    <ol class="list-decimal list-inside mt-2 space-y-1">
                        <li>Open WhatsApp conversation</li>
                        <li>Tap on contact/group name → More → Export chat</li>
                        <li>Choose "Without Media" and save as .txt file</li>
                        <li>Upload the file here for analysis</li>
                    </ol>
                </div>
            </div>

            <!-- Analysis Results -->
            <div v-if="analysisResults" class="space-y-6">
                <!-- Overview Metrics -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <MessageSquare class="h-8 w-8 text-green-600" />
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Messages</p>
                                <p class="text-2xl font-bold text-gray-900">{{ analysisResults.totalMessages.toLocaleString() }}</p>
                                <p class="text-sm text-green-600">{{ analysisResults.avgPerDay }} per day</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <Users class="h-8 w-8 text-blue-600" />
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Participants</p>
                                <p class="text-2xl font-bold text-gray-900">{{ analysisResults.participants }}</p>
                                <p class="text-sm text-blue-600">{{ analysisResults.mostActive }} most active</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <Clock class="h-8 w-8 text-purple-600" />
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Response Time</p>
                                <p class="text-2xl font-bold text-gray-900">{{ analysisResults.avgResponseTime }}</p>
                                <p class="text-sm text-purple-600">Average response</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <TrendingUp class="h-8 w-8 text-orange-600" />
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Engagement Score</p>
                                <p class="text-2xl font-bold text-gray-900">{{ analysisResults.engagementScore }}/10</p>
                                <p class="text-sm text-orange-600">{{ analysisResults.engagementLevel }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sentiment Analysis -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Sentiment Distribution -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Sentiment Analysis</h3>
                        <div class="space-y-4">
                            <div v-for="sentiment in analysisResults.sentiments" :key="sentiment.type" class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div :class="['w-4 h-4 rounded-full mr-3', sentiment.color]"></div>
                                    <span class="text-sm font-medium text-gray-900">{{ sentiment.type }}</span>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-semibold text-gray-900">{{ sentiment.percentage }}%</p>
                                    <p class="text-xs text-gray-500">{{ sentiment.count }} messages</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Activity Timeline -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Activity Timeline</h3>
                        <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                            <div class="text-center">
                                <BarChart3 class="h-12 w-12 text-gray-400 mx-auto mb-2" />
                                <p class="text-gray-600">Activity Timeline Chart</p>
                                <p class="text-sm text-gray-500">Chart.js integration</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Key Insights -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">AI-Generated Insights</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <h4 class="font-medium text-gray-900">Communication Patterns</h4>
                            <div v-for="insight in analysisResults.insights" :key="insight.id" class="flex items-start p-3 bg-blue-50 rounded-lg">
                                <Brain class="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                                <div>
                                    <p class="text-sm font-medium text-blue-900">{{ insight.title }}</p>
                                    <p class="text-sm text-blue-700">{{ insight.description }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <h4 class="font-medium text-gray-900">Recommendations</h4>
                            <div v-for="recommendation in analysisResults.recommendations" :key="recommendation.id" class="flex items-start p-3 bg-green-50 rounded-lg">
                                <Lightbulb class="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                                <div>
                                    <p class="text-sm font-medium text-green-900">{{ recommendation.title }}</p>
                                    <p class="text-sm text-green-700">{{ recommendation.description }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Word Cloud & Keywords -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Most Used Words -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Most Used Words</h3>
                        <div class="space-y-3">
                            <div v-for="word in analysisResults.topWords" :key="word.word" class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-900">{{ word.word }}</span>
                                <div class="flex items-center">
                                    <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-green-500 h-2 rounded-full" :style="{ width: (word.count / analysisResults.topWords[0].count) * 100 + '%' }"></div>
                                    </div>
                                    <span class="text-sm text-gray-600">{{ word.count }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Business Topics -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Business Topics Discussed</h3>
                        <div class="space-y-3">
                            <div v-for="topic in analysisResults.businessTopics" :key="topic.topic" class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-900">{{ topic.topic }}</span>
                                <div class="flex items-center">
                                    <span class="text-sm text-gray-600 mr-2">{{ topic.mentions }} mentions</span>
                                    <span :class="[
                                        'px-2 py-1 text-xs font-medium rounded-full',
                                        topic.sentiment === 'Positive' ? 'bg-green-100 text-green-800' :
                                        topic.sentiment === 'Neutral' ? 'bg-gray-100 text-gray-800' :
                                        'bg-red-100 text-red-800'
                                    ]">
                                        {{ topic.sentiment }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Service Analysis -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Customer Service Performance</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <CheckCircle class="h-8 w-8 text-green-600 mx-auto mb-2" />
                            <h4 class="font-medium text-green-900">Issues Resolved</h4>
                            <p class="text-2xl font-bold text-green-900">{{ analysisResults.customerService.resolved }}%</p>
                        </div>
                        <div class="text-center p-4 bg-yellow-50 rounded-lg">
                            <Clock class="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                            <h4 class="font-medium text-yellow-900">Avg Resolution Time</h4>
                            <p class="text-2xl font-bold text-yellow-900">{{ analysisResults.customerService.resolutionTime }}</p>
                        </div>
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <Star class="h-8 w-8 text-blue-600 mx-auto mb-2" />
                            <h4 class="font-medium text-blue-900">Customer Satisfaction</h4>
                            <p class="text-2xl font-bold text-blue-900">{{ analysisResults.customerService.satisfaction }}/5</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-green-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-green-900">Advanced WhatsApp Analytics Coming Soon</h3>
                        <p class="text-green-700 mt-1">
                            Enhanced WhatsApp analysis features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-green-700 mt-2 space-y-1">
                            <li>Real-time WhatsApp Business API integration</li>
                            <li>Automated customer intent recognition</li>
                            <li>Multi-language sentiment analysis</li>
                            <li>Chatbot performance optimization</li>
                            <li>Customer journey mapping from conversations</li>
                            <li>Automated response suggestions</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Upload,
    Download,
    MessageSquare,
    Users,
    Clock,
    TrendingUp,
    BarChart3,
    Brain,
    Lightbulb,
    CheckCircle,
    Star,
    Info
} from 'lucide-vue-next';
import { ref, onMounted } from 'vue';

// Reactive state
const analysisResults = ref(null);

// Demo analysis data
const demoAnalysis = {
    totalMessages: 2847,
    avgPerDay: 23,
    participants: 8,
    mostActive: 'Customer Service',
    avgResponseTime: '12 min',
    engagementScore: 8.5,
    engagementLevel: 'High',
    sentiments: [
        { type: 'Positive', percentage: 65, count: 1850, color: 'bg-green-500' },
        { type: 'Neutral', percentage: 28, count: 797, color: 'bg-gray-500' },
        { type: 'Negative', percentage: 7, count: 200, color: 'bg-red-500' }
    ],
    insights: [
        {
            id: 1,
            title: 'Peak Activity Hours',
            description: 'Most conversations happen between 9 AM - 5 PM on weekdays'
        },
        {
            id: 2,
            title: 'Response Pattern',
            description: 'Customer service team responds fastest to product inquiries'
        },
        {
            id: 3,
            title: 'Conversation Length',
            description: 'Average conversation consists of 15 messages before resolution'
        }
    ],
    recommendations: [
        {
            id: 1,
            title: 'Improve Weekend Coverage',
            description: 'Consider adding weekend support to reduce response times'
        },
        {
            id: 2,
            title: 'Automate Common Queries',
            description: 'Implement chatbot for frequently asked questions about pricing'
        },
        {
            id: 3,
            title: 'Proactive Follow-up',
            description: 'Send follow-up messages for unresolved conversations after 24 hours'
        }
    ],
    topWords: [
        { word: 'tire', count: 456 },
        { word: 'price', count: 389 },
        { word: 'delivery', count: 234 },
        { word: 'quality', count: 198 },
        { word: 'service', count: 167 },
        { word: 'mining', count: 145 }
    ],
    businessTopics: [
        { topic: 'Product Pricing', mentions: 89, sentiment: 'Neutral' },
        { topic: 'Delivery Schedule', mentions: 67, sentiment: 'Positive' },
        { topic: 'Technical Support', mentions: 45, sentiment: 'Positive' },
        { topic: 'Complaints', mentions: 23, sentiment: 'Negative' },
        { topic: 'New Products', mentions: 34, sentiment: 'Positive' }
    ],
    customerService: {
        resolved: 87,
        resolutionTime: '2.5 hours',
        satisfaction: 4.2
    }
};

// Event handlers
const uploadChat = () => {
    alert('Chat upload functionality coming soon!\n\nFeatures:\n- Drag & drop WhatsApp export files\n- Automatic parsing and analysis\n- Real-time processing status\n- Support for multiple chat formats');
};

const selectFile = () => {
    // Create file input element
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.txt,.csv';
    input.onchange = (event) => {
        const file = (event.target as HTMLInputElement).files?.[0];
        if (file) {
            alert(`File selected: ${file.name}\n\nProcessing chat analysis...\n\nThis will analyze:\n- Message patterns\n- Sentiment analysis\n- Response times\n- Customer satisfaction`);
            // Show demo analysis after "processing"
            setTimeout(() => {
                analysisResults.value = demoAnalysis;
            }, 2000);
        }
    };
    input.click();
};

const showDemo = () => {
    analysisResults.value = demoAnalysis;
    alert('Demo analysis loaded!\n\nThis shows sample analysis of a WhatsApp business chat with 2,847 messages from a tire company customer service conversation.');
};

const exportAnalysis = () => {
    if (!analysisResults.value) {
        alert('No analysis to export. Please upload a chat file first.');
        return;
    }

    try {
        const reportData = {
            summary: {
                totalMessages: analysisResults.value.totalMessages,
                participants: analysisResults.value.participants,
                avgResponseTime: analysisResults.value.avgResponseTime,
                engagementScore: analysisResults.value.engagementScore
            },
            sentiments: analysisResults.value.sentiments,
            insights: analysisResults.value.insights,
            recommendations: analysisResults.value.recommendations,
            topWords: analysisResults.value.topWords,
            businessTopics: analysisResults.value.businessTopics,
            customerService: analysisResults.value.customerService,
            generatedAt: new Date().toISOString()
        };

        const csvContent = [
            ['WhatsApp Chat Analysis Report'].join(','),
            ['Generated:', new Date().toLocaleDateString('id-ID')].join(','),
            [],
            ['Summary Metrics'].join(','),
            ['Total Messages', analysisResults.value.totalMessages].join(','),
            ['Participants', analysisResults.value.participants].join(','),
            ['Avg Response Time', analysisResults.value.avgResponseTime].join(','),
            ['Engagement Score', `${analysisResults.value.engagementScore}/10`].join(','),
            [],
            ['Sentiment Analysis'].join(','),
            ['Sentiment', 'Percentage', 'Count'].join(','),
            ...analysisResults.value.sentiments.map(s => [s.type, `${s.percentage}%`, s.count].join(',')),
            [],
            ['Top Words'].join(','),
            ['Word', 'Count'].join(','),
            ...analysisResults.value.topWords.map(w => [w.word, w.count].join(',')),
            [],
            ['Business Topics'].join(','),
            ['Topic', 'Mentions', 'Sentiment'].join(','),
            ...analysisResults.value.businessTopics.map(t => [t.topic, t.mentions, t.sentiment].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `whatsapp_analysis_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('WhatsApp analysis exported successfully!');
    } catch (error) {
        console.error('Error exporting analysis:', error);
        alert('Failed to export analysis.');
    }
};

// Initialize on mount
onMounted(() => {
    // Load any saved analysis
});
</script>
