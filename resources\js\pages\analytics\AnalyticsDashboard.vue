<template>
    <AppLayout>
        <div class="space-y-6">
            <div class="border-b border-gray-200 pb-4">
                <h1 class="text-2xl font-bold text-gray-900">Dashboard Analitik Terpadu</h1>
                <p class="mt-2 text-gray-600">Comprehensive analytics dashboard for business insights.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- Stats Cards -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                <TrendingUp class="w-5 h-5 text-white" />
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Sales</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ formatCurrency(analytics.totalSales) }}</p>
                            <p class="text-xs text-green-600 mt-1">+{{ analytics.salesGrowth }}% from last month</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                <Users class="w-5 h-5 text-white" />
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Active Customers</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ analytics.activeCustomers.toLocaleString() }}</p>
                            <p class="text-xs text-green-600 mt-1">+{{ analytics.customerGrowth }}% new this month</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                <Package class="w-5 h-5 text-white" />
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Products Sold</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ analytics.productsSold.toLocaleString() }}</p>
                            <p class="text-xs text-blue-600 mt-1">{{ analytics.topProduct }} is trending</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                <DollarSign class="w-5 h-5 text-white" />
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Monthly Revenue</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ formatCurrency(analytics.monthlyRevenue) }}</p>
                            <p class="text-xs text-purple-600 mt-1">{{ analytics.profitMargin }}% profit margin</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Sales Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Sales Trend</h3>
                        <select v-model="selectedPeriod" class="text-sm border border-gray-300 rounded-md px-3 py-1">
                            <option value="7days">Last 7 Days</option>
                            <option value="30days">Last 30 Days</option>
                            <option value="90days">Last 90 Days</option>
                        </select>
                    </div>
                    <div class="h-64">
                        <canvas ref="salesChartRef"></canvas>
                    </div>
                </div>

                <!-- Product Performance Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Top Products</h3>
                        <BarChart3 class="w-5 h-5 text-gray-400" />
                    </div>
                    <div class="h-64">
                        <canvas ref="productChartRef"></canvas>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Activities</h3>
                    <button
                        @click="refreshData"
                        :disabled="isLoading"
                        class="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                    >
                        <RefreshCw :class="['w-4 h-4 mr-1', { 'animate-spin': isLoading }]" />
                        Refresh
                    </button>
                </div>
                <div class="space-y-3">
                    <div v-for="activity in recentActivities" :key="activity.id" class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div class="flex-shrink-0">
                            <div :class="['w-8 h-8 rounded-full flex items-center justify-center', activity.color]">
                                <component :is="activity.icon" class="w-4 h-4 text-white" />
                            </div>
                        </div>
                        <div class="ml-3 flex-1">
                            <p class="text-sm font-medium text-gray-900">{{ activity.title }}</p>
                            <p class="text-xs text-gray-500">{{ activity.description }}</p>
                        </div>
                        <div class="text-xs text-gray-400">
                            {{ formatTime(activity.timestamp) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
