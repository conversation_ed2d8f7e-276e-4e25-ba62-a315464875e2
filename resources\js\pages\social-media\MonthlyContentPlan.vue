<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Monthly Content Plan</h1>
                    <p class="mt-2 text-gray-600">Plan and organize your social media content for the month</p>
                </div>
                <div class="flex space-x-3">
                    <select v-model="selectedMonth" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                        <option v-for="(month, index) in months" :key="index" :value="index">
                            {{ month }} {{ currentYear }}
                        </option>
                    </select>
                    <button
                        @click="generatePlan"
                        class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center"
                    >
                        <Sparkles class="h-4 w-4 mr-2" />
                        Auto Generate
                    </button>
                    <button
                        @click="exportPlan"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export Plan
                    </button>
                </div>
            </div>

            <!-- Plan Overview -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Calendar class="h-8 w-8 text-blue-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Posts</p>
                            <p class="text-2xl font-bold text-gray-900">{{ planStats.totalPosts }}</p>
                            <p class="text-sm text-blue-600">{{ planStats.postsPerWeek }} per week</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Image class="h-8 w-8 text-green-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Visual Content</p>
                            <p class="text-2xl font-bold text-gray-900">{{ planStats.visualContent }}%</p>
                            <p class="text-sm text-green-600">Images & Videos</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Target class="h-8 w-8 text-purple-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Platforms</p>
                            <p class="text-2xl font-bold text-gray-900">{{ planStats.platforms }}</p>
                            <p class="text-sm text-purple-600">Active channels</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <TrendingUp class="h-8 w-8 text-orange-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Completion</p>
                            <p class="text-2xl font-bold text-gray-900">{{ planStats.completion }}%</p>
                            <p class="text-sm text-orange-600">Plan progress</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Calendar -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Content Calendar - {{ months[selectedMonth] }} {{ currentYear }}</h3>
                    <div class="flex space-x-3">
                        <button
                            @click="addContent"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                        >
                            <Plus class="h-4 w-4 mr-2" />
                            Add Content
                        </button>
                        <button class="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center">
                            <Filter class="h-4 w-4 mr-2" />
                            Filter
                        </button>
                    </div>
                </div>

                <!-- Calendar Grid -->
                <div class="p-6">
                    <!-- Weekday Headers -->
                    <div class="grid grid-cols-7 gap-1 mb-4">
                        <div v-for="day in weekdays" :key="day" class="p-2 text-center text-sm font-medium text-gray-500">
                            {{ day }}
                        </div>
                    </div>

                    <!-- Calendar Days -->
                    <div class="grid grid-cols-7 gap-1">
                        <div 
                            v-for="day in calendarDays" 
                            :key="day.date"
                            class="min-h-[120px] p-2 border border-gray-200 rounded-lg"
                            :class="day.isCurrentMonth ? 'bg-white' : 'bg-gray-50'"
                        >
                            <div class="flex items-center justify-between mb-2">
                                <span 
                                    :class="[
                                        'text-sm font-medium',
                                        day.isToday ? 'text-blue-600' : day.isCurrentMonth ? 'text-gray-900' : 'text-gray-400'
                                    ]"
                                >
                                    {{ day.dayNumber }}
                                </span>
                                <button 
                                    v-if="day.isCurrentMonth"
                                    @click="addContentToDay(day)"
                                    class="text-gray-400 hover:text-blue-600"
                                >
                                    <Plus class="h-3 w-3" />
                                </button>
                            </div>

                            <!-- Content Items -->
                            <div class="space-y-1">
                                <div 
                                    v-for="content in day.content" 
                                    :key="content.id"
                                    @click="editContent(content)"
                                    class="p-1 text-xs rounded cursor-pointer hover:shadow-sm"
                                    :class="getContentTypeColor(content.type)"
                                >
                                    <div class="flex items-center justify-between">
                                        <span class="truncate">{{ content.title }}</span>
                                        <component :is="getPlatformIcon(content.platform)" class="h-3 w-3 flex-shrink-0" />
                                    </div>
                                    <div class="text-xs opacity-75">{{ content.time }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Types Distribution -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Content Types -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Content Types Distribution</h3>
                    <div class="space-y-4">
                        <div v-for="type in contentTypes" :key="type.name" class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div :class="['w-4 h-4 rounded-full mr-3', type.color]"></div>
                                <span class="text-sm font-medium text-gray-900">{{ type.name }}</span>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">{{ type.count }}</p>
                                <p class="text-xs text-gray-500">{{ type.percentage }}%</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Platform Distribution -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Platform Distribution</h3>
                    <div class="space-y-4">
                        <div v-for="platform in platformDistribution" :key="platform.name" class="flex items-center justify-between">
                            <div class="flex items-center">
                                <component :is="getPlatformIcon(platform.name.toLowerCase())" class="h-4 w-4 mr-3 text-gray-600" />
                                <span class="text-sm font-medium text-gray-900">{{ platform.name }}</span>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">{{ platform.posts }}</p>
                                <p class="text-xs text-gray-500">{{ platform.percentage }}%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Templates -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Content Templates</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div 
                        v-for="template in contentTemplates" 
                        :key="template.id"
                        @click="useTemplate(template)"
                        class="p-4 border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-shadow"
                    >
                        <div class="flex items-center mb-3">
                            <component :is="template.icon" class="h-6 w-6 text-blue-600 mr-3" />
                            <h4 class="font-medium text-gray-900">{{ template.name }}</h4>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">{{ template.description }}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500">{{ template.category }}</span>
                            <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">Use Template</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-indigo-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-indigo-900">Advanced Content Planning Coming Soon</h3>
                        <p class="text-indigo-700 mt-1">
                            Enhanced content planning features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-indigo-700 mt-2 space-y-1">
                            <li>AI-powered content suggestions based on trends</li>
                            <li>Automated optimal posting time recommendations</li>
                            <li>Content performance prediction</li>
                            <li>Bulk content import and scheduling</li>
                            <li>Team collaboration and approval workflows</li>
                            <li>Integration with design tools and asset libraries</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Sparkles,
    Download,
    Calendar,
    Image,
    Target,
    TrendingUp,
    Plus,
    Filter,
    MessageSquare,
    FileText,
    Video,
    Info
} from 'lucide-vue-next';
import { ref, computed, onMounted } from 'vue';

// Types
interface ContentItem {
    id: string;
    title: string;
    type: string;
    platform: string;
    time: string;
    status: string;
}

interface CalendarDay {
    date: string;
    dayNumber: number;
    isCurrentMonth: boolean;
    isToday: boolean;
    content: ContentItem[];
}

// Reactive state
const selectedMonth = ref(new Date().getMonth());
const currentYear = ref(new Date().getFullYear());

// Constants
const months = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
];

const weekdays = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];

// Plan statistics
const planStats = ref({
    totalPosts: 28,
    postsPerWeek: 7,
    visualContent: 75,
    platforms: 4,
    completion: 68
});

// Content types
const contentTypes = ref([
    { name: 'Product Showcase', count: 8, percentage: 29, color: 'bg-blue-500' },
    { name: 'Educational', count: 6, percentage: 21, color: 'bg-green-500' },
    { name: 'Behind the Scenes', count: 5, percentage: 18, color: 'bg-purple-500' },
    { name: 'Customer Stories', count: 4, percentage: 14, color: 'bg-orange-500' },
    { name: 'Industry News', count: 3, percentage: 11, color: 'bg-red-500' },
    { name: 'Promotional', count: 2, percentage: 7, color: 'bg-yellow-500' }
]);

// Platform distribution
const platformDistribution = ref([
    { name: 'Instagram', posts: 12, percentage: 43 },
    { name: 'Facebook', posts: 8, percentage: 29 },
    { name: 'LinkedIn', posts: 5, percentage: 18 },
    { name: 'Twitter', posts: 3, percentage: 11 }
]);

// Content templates
const contentTemplates = ref([
    {
        id: 'product-showcase',
        name: 'Product Showcase',
        description: 'Highlight product features and benefits',
        category: 'Product',
        icon: Image
    },
    {
        id: 'educational-post',
        name: 'Educational Post',
        description: 'Share industry knowledge and tips',
        category: 'Educational',
        icon: FileText
    },
    {
        id: 'video-content',
        name: 'Video Content',
        description: 'Engaging video posts and stories',
        category: 'Video',
        icon: Video
    },
    {
        id: 'customer-story',
        name: 'Customer Story',
        description: 'Feature customer success stories',
        category: 'Social Proof',
        icon: MessageSquare
    },
    {
        id: 'behind-scenes',
        name: 'Behind the Scenes',
        description: 'Show company culture and processes',
        category: 'Brand',
        icon: Image
    },
    {
        id: 'industry-news',
        name: 'Industry News',
        description: 'Share relevant industry updates',
        category: 'News',
        icon: FileText
    }
]);

// Computed properties
const calendarDays = computed(() => {
    const year = currentYear.value;
    const month = selectedMonth.value;
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days: CalendarDay[] = [];
    const today = new Date();

    for (let i = 0; i < 42; i++) {
        const currentDate = new Date(startDate);
        currentDate.setDate(startDate.getDate() + i);

        const isCurrentMonth = currentDate.getMonth() === month;
        const isToday = currentDate.toDateString() === today.toDateString();

        // Generate sample content for some days
        const content: ContentItem[] = [];
        if (isCurrentMonth && Math.random() > 0.7) {
            content.push({
                id: `content-${i}`,
                title: getSampleContentTitle(),
                type: getSampleContentType(),
                platform: getSamplePlatform(),
                time: getSampleTime(),
                status: 'scheduled'
            });
        }

        days.push({
            date: currentDate.toISOString().split('T')[0],
            dayNumber: currentDate.getDate(),
            isCurrentMonth,
            isToday,
            content
        });
    }

    return days;
});

// Utility functions
const getSampleContentTitle = (): string => {
    const titles = [
        'New Product Launch',
        'Maintenance Tips',
        'Customer Success Story',
        'Industry Insights',
        'Behind the Scenes',
        'Product Showcase'
    ];
    return titles[Math.floor(Math.random() * titles.length)];
};

const getSampleContentType = (): string => {
    const types = ['image', 'video', 'carousel', 'story'];
    return types[Math.floor(Math.random() * types.length)];
};

const getSamplePlatform = (): string => {
    const platforms = ['instagram', 'facebook', 'linkedin', 'twitter'];
    return platforms[Math.floor(Math.random() * platforms.length)];
};

const getSampleTime = (): string => {
    const hours = Math.floor(Math.random() * 12) + 8; // 8 AM to 8 PM
    const minutes = Math.random() > 0.5 ? '00' : '30';
    return `${hours}:${minutes}`;
};

const getContentTypeColor = (type: string): string => {
    const colors: Record<string, string> = {
        image: 'bg-blue-100 text-blue-800',
        video: 'bg-red-100 text-red-800',
        carousel: 'bg-green-100 text-green-800',
        story: 'bg-purple-100 text-purple-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
};

const getPlatformIcon = (platform: string) => {
    // Using MessageSquare for all platforms as placeholder
    return MessageSquare;
};

// Event handlers
const generatePlan = () => {
    alert('Generating AI-powered content plan...\n\nAnalyzing:\n- Industry trends\n- Optimal posting times\n- Content performance history\n- Competitor analysis\n\nPlan will be ready in 2-3 minutes.');
};

const exportPlan = () => {
    alert('Exporting content plan...\n\nFormats available:\n- CSV for spreadsheet import\n- PDF for presentation\n- JSON for system integration\n- Calendar file for scheduling tools');
};

const addContent = () => {
    alert('Opening content creator...\n\nFeatures:\n- Content templates\n- AI writing assistance\n- Media library\n- Multi-platform optimization');
};

const addContentToDay = (day: CalendarDay) => {
    alert(`Adding content for ${day.dayNumber}/${selectedMonth.value + 1}/${currentYear.value}`);
};

const editContent = (content: ContentItem) => {
    alert(`Editing content: ${content.title}`);
};

const useTemplate = (template: any) => {
    alert(`Using template: ${template.name}\n\nThis will open the content creator with pre-filled template content.`);
};

// Initialize on mount
onMounted(() => {
    // Load content plan data
});
</script>
