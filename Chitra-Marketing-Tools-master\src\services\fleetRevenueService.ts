import axios from 'axios';
import <PERSON> from 'papapar<PERSON>';
import fallbackData from '../data/fleetRevenueData.json';

// URLs for the datasets
const FLEET_LIST_URL = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vSRFlBtJQvRB5xMg5276wnm6-aMeR9oli3IxMAG15QHbu79qh2Le8BEUINgomb72j0l60CxExqXAXJy/pub?output=csv';
const SALES_DATA_URL = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vSrcOlsVNOmwifGY-dHqFbQHrkUuYMKGSE9yi9PrgwcIVa723xyjJ8vcP4cQhVNvGT_sVpzppeUkZFO/pub?gid=0&single=true&output=csv';

// Add CORS proxy if needed
const useCorsProxy = true;
const corsProxy = 'https://corsproxy.io/?';

// Get the actual URLs with CORS proxy if needed
const getFleetListUrl = () => useCorsProxy ? `${corsProxy}${encodeURIComponent(FLEET_LIST_URL)}` : FLEET_LIST_URL;
const getSalesDataUrl = () => useCorsProxy ? `${corsProxy}${encodeURIComponent(SALES_DATA_URL)}` : SALES_DATA_URL;

// Interface for Fleet List data
export interface FleetListItem {
  CUSTOMER: string;
  QTY: string;
  'Mat Grp Desc': string;
  'Revenue in Doc Curr.': string;
  'Mat Grp1 Desc': string;
  [key: string]: any;
}

// Interface for Sales data
export interface SalesItem {
  customer: string;
  'Tire Size': string;
  'Total Tire': string;
  Forecast: string;
  Status: string;
  Date?: string;
  Revenue?: number;
  CustomerCount?: number;
  [key: string]: any;
}

// Interface for combined data
export interface FleetRevenueItem {
  id: string;
  customer: string;
  tireSize: string;
  customerTotalTire: string;
  forecastTire: string;
  actualRevenue: string;
}

/**
 * Extract tire size from Mat Grp Desc
 * @param matGrpDesc The Mat Grp Desc string
 * @returns The extracted tire size or empty string if not found
 */
const extractTireSize = (matGrpDesc: string): string => {
  // Common tire size patterns
  const patterns = [
    /\b\d+\.\d+R\d+\b/i,  // e.g., 27.00R49
    /\bR\d+\b/i,          // e.g., R25, R29
    /\b\d+R\d+\b/i,       // e.g., 24R35
  ];

  for (const pattern of patterns) {
    const match = matGrpDesc.match(pattern);
    if (match) {
      return match[0].toUpperCase();
    }
  }

  return '';
}

/**
 * Check if two tire sizes match
 * @param size1 First tire size
 * @param size2 Second tire size
 * @returns True if sizes match, false otherwise
 */
const tireSizesMatch = (size1: string, size2: string): boolean => {
  // Normalize sizes for comparison
  const normalizedSize1 = size1.toUpperCase().trim();
  const normalizedSize2 = size2.toUpperCase().trim();

  // Direct match
  if (normalizedSize1 === normalizedSize2) {
    return true;
  }

  // Check if one contains the other
  if (normalizedSize1.includes(normalizedSize2) || normalizedSize2.includes(normalizedSize1)) {
    return true;
  }

  // Extract numbers for partial matching
  const numbers1 = normalizedSize1.match(/\d+/g) || [];
  const numbers2 = normalizedSize2.match(/\d+/g) || [];

  // If both have the same numbers in the same order, consider it a match
  if (numbers1.length > 0 && numbers1.length === numbers2.length) {
    return numbers1.every((num, index) => num === numbers2[index]);
  }

  return false;
}

/**
 * Fetches and processes fleet list data
 */
export const fetchFleetListData = async (): Promise<FleetListItem[]> => {
  try {
    // Try to use local fallback data first for development
    const useLocalData = true; // Set to true to use local data instead of API

    if (useLocalData) {
      console.log('Using local fleet list data');
      return Promise.resolve(fallbackData.fleetList as FleetListItem[]);
    }

    const url = getFleetListUrl();
    console.log('Fetching fleet list data from:', url);
    const response = await axios.get(url, {
      headers: {
        'Accept': 'text/csv; charset=utf-8',
      }
    });

    // Log the first part of the response to debug
    console.log('Fleet list data response preview:', response.data.substring(0, 200) + '...');

    return new Promise((resolve, reject) => {
      Papa.parse(response.data, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          if (results.data && Array.isArray(results.data)) {
            console.log('Fleet list raw data sample:', results.data.slice(0, 2));
            console.log('Fleet list columns:', results.meta.fields);

            // Check if required columns exist
            const requiredColumns = ['CUSTOMER', 'Mat Grp Desc', 'Revenue in Doc Curr.', 'Mat Grp1 Desc'];
            const missingColumns = requiredColumns.filter(col =>
              !results.meta.fields?.includes(col)
            );

            if (missingColumns.length > 0) {
              console.error('Missing required columns in fleet list data:', missingColumns);
              reject(new Error(`Missing required columns: ${missingColumns.join(', ')}`));
              return;
            }

            // Filter data according to requirements
            const filteredData = results.data
              .filter((item: any) =>
                item['Mat Grp1 Desc'] === 'CP TIRE' &&
                item['Mat Grp Desc'] &&
                item['Mat Grp Desc'].includes('TIRES')
              );

            console.log(`Successfully parsed ${filteredData.length} fleet list records`);
            if (filteredData.length > 0) {
              console.log('Sample filtered fleet list data:', filteredData[0]);
            }

            resolve(filteredData as FleetListItem[]);
          } else {
            console.error('Invalid fleet list data structure:', results);
            reject(new Error('Failed to parse fleet list CSV data'));
          }
        },
        error: (error) => {
          console.error('Error parsing fleet list CSV:', error);
          reject(error);
        }
      });
    });
  } catch (error) {
    console.error('Error fetching fleet list data:', error);
    console.log('Falling back to local fleet list data');
    return fallbackData.fleetList as FleetListItem[];
  }
};

/**
 * Fetches and processes sales data
 */
export const fetchSalesData = async (): Promise<SalesItem[]> => {
  try {
    // Try to use local fallback data first for development
    const useLocalData = true; // Set to true to use local data instead of API

    if (useLocalData) {
      console.log('Using local sales data');
      return Promise.resolve(fallbackData.salesData as SalesItem[]);
    }

    const url = getSalesDataUrl();
    console.log('Fetching sales data from:', url);
    const response = await axios.get(url, {
      headers: {
        'Accept': 'text/csv; charset=utf-8',
      }
    });

    // Log the first part of the response to debug
    console.log('Sales data response preview:', response.data.substring(0, 200) + '...');

    return new Promise((resolve, reject) => {
      Papa.parse(response.data, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          if (results.data && Array.isArray(results.data)) {
            console.log('Sales raw data sample:', results.data.slice(0, 2));
            console.log('Sales columns:', results.meta.fields);

            // Check if required columns exist
            const requiredColumns = ['customer', 'Tire Size', 'Total Tire', 'Forecast', 'Status'];
            const missingColumns = requiredColumns.filter(col =>
              !results.meta.fields?.includes(col)
            );

            if (missingColumns.length > 0) {
              console.error('Missing required columns in sales data:', missingColumns);
              reject(new Error(`Missing required columns: ${missingColumns.join(', ')}`));
              return;
            }

            // Filter data according to requirements
            const filteredData = results.data
              .filter((item: any) => item.Status === 'Aktiv');

            console.log(`Successfully parsed ${filteredData.length} sales records`);
            if (filteredData.length > 0) {
              console.log('Sample filtered sales data:', filteredData[0]);
            }

            resolve(filteredData as SalesItem[]);
          } else {
            console.error('Invalid sales data structure:', results);
            reject(new Error('Failed to parse sales CSV data'));
          }
        },
        error: (error) => {
          console.error('Error parsing sales CSV:', error);
          reject(error);
        }
      });
    });
  } catch (error) {
    console.error('Error fetching sales data:', error);
    console.log('Falling back to local sales data');
    return fallbackData.salesData as SalesItem[];
  }
};

/**
 * Combines fleet list and sales data
 */
export const combineFleetRevenueData = async (): Promise<FleetRevenueItem[]> => {
  try {
    // Fetch both datasets
    const fleetListData = await fetchFleetListData();
    const salesData = await fetchSalesData();

    // Process and combine data
    const combinedData: FleetRevenueItem[] = [];
    const processedPairs = new Set<string>(); // To track processed customer-tireSize pairs

    // Group fleet list data by customer and tire size
    const fleetListByCustomer: Record<string, Record<string, { qty: number, revenue: number }>> = {};

    fleetListData.forEach(item => {
      const customer = item.CUSTOMER.trim().toUpperCase();
      const tireSize = extractTireSize(item['Mat Grp Desc']);

      if (tireSize) {
        if (!fleetListByCustomer[customer]) {
          fleetListByCustomer[customer] = {};
        }

        if (!fleetListByCustomer[customer][tireSize]) {
          fleetListByCustomer[customer][tireSize] = { qty: 0, revenue: 0 };
        }

        // Add quantities and revenue
        const qty = parseInt(item.QTY) || 0;
        const revenue = parseFloat(item['Revenue in Doc Curr.'].replace(/,/g, '')) || 0;

        fleetListByCustomer[customer][tireSize].qty += qty;
        fleetListByCustomer[customer][tireSize].revenue += revenue;
      }
    });

    // Match sales data with fleet list data
    salesData.forEach(salesItem => {
      const salesCustomer = salesItem.customer.trim().toUpperCase();
      const salesTireSize = salesItem['Tire Size'].trim().toUpperCase();

      // Find matching customer in fleet list
      Object.keys(fleetListByCustomer).forEach(fleetCustomer => {
        // Check if customers match (exact or partial match)
        if (fleetCustomer.includes(salesCustomer) || salesCustomer.includes(fleetCustomer)) {
          // Find matching tire size
          Object.keys(fleetListByCustomer[fleetCustomer]).forEach(fleetTireSize => {
            if (tireSizesMatch(fleetTireSize, salesTireSize)) {
              const pairKey = `${fleetCustomer}-${fleetTireSize}`;

              // Skip if we've already processed this pair
              if (!processedPairs.has(pairKey)) {
                processedPairs.add(pairKey);

                const { qty, revenue } = fleetListByCustomer[fleetCustomer][fleetTireSize];

                combinedData.push({
                  id: `rev-${combinedData.length + 1}`,
                  customer: fleetCustomer,
                  tireSize: fleetTireSize,
                  customerTotalTire: qty.toString(),
                  forecastTire: salesItem.Forecast || '0',
                  actualRevenue: revenue.toLocaleString()
                });
              }
            }
          });
        }
      });
    });

    console.log(`Combined ${combinedData.length} fleet revenue records`);
    return combinedData;
  } catch (error) {
    console.error('Error combining fleet revenue data:', error);
    throw error;
  }
};

// Fallback data in case the API fails
export const FALLBACK_FLEET_REVENUE: FleetRevenueItem[] = [
  {
    id: "rev-1",
    customer: "ADARO INDONESIA",
    tireSize: "27.00R49",
    customerTotalTire: "90",
    forecastTire: "120",
    actualRevenue: "1,250,000,000"
  },
  {
    id: "rev-1a",
    customer: "ADARO INDONESIA",
    tireSize: "11.00R20",
    customerTotalTire: "48",
    forecastTire: "64",
    actualRevenue: "96,000,000"
  },
  {
    id: "rev-2a",
    customer: "BERAU COAL",
    tireSize: "12.00R24",
    customerTotalTire: "60",
    forecastTire: "80",
    actualRevenue: "150,000,000"
  },
  {
    id: "rev-2",
    customer: "BERAU COAL",
    tireSize: "24.00R35",
    customerTotalTire: "72",
    forecastTire: "96",
    actualRevenue: "864,000,000"
  },
  {
    id: "rev-3",
    customer: "KALTIM PRIMA COAL",
    tireSize: "27.00R49",
    customerTotalTire: "60",
    forecastTire: "80",
    actualRevenue: "720,000,000"
  },
  {
    id: "rev-4",
    customer: "KIDECO JAYA AGUNG",
    tireSize: "24.00R35",
    customerTotalTire: "48",
    forecastTire: "64",
    actualRevenue: "576,000,000"
  },
  {
    id: "rev-5",
    customer: "BUKIT ASAM",
    tireSize: "27.00R49",
    customerTotalTire: "36",
    forecastTire: "48",
    actualRevenue: "432,000,000"
  },
  {
    id: "rev-6",
    customer: "CIPTA KRIDATAMA",
    tireSize: "27.00R49",
    customerTotalTire: "66",
    forecastTire: "88",
    actualRevenue: "792,000,000"
  },
  {
    id: "rev-7",
    customer: "CIPTA KRIDATAMA",
    tireSize: "33.00R51",
    customerTotalTire: "42",
    forecastTire: "56",
    actualRevenue: "630,000,000"
  },
  {
    id: "rev-8",
    customer: "ABADI JAYA LAXIMINDO",
    tireSize: "24.00R35",
    customerTotalTire: "42",
    forecastTire: "56",
    actualRevenue: "504,000,000"
  },
  {
    id: "rev-9",
    customer: "SIS",
    tireSize: "27.00R49",
    customerTotalTire: "78",
    forecastTire: "104",
    actualRevenue: "936,000,000"
  },
  {
    id: "rev-10",
    customer: "SIS",
    tireSize: "33.00R51",
    customerTotalTire: "48",
    forecastTire: "64",
    actualRevenue: "720,000,000"
  },
  {
    id: "rev-11",
    customer: "PETROSEA",
    tireSize: "24.00R35",
    customerTotalTire: "48",
    forecastTire: "64",
    actualRevenue: "576,000,000"
  },
  {
    id: "rev-12",
    customer: "MANDIRI HERINDO ADIPERKASA",
    tireSize: "27.00R49",
    customerTotalTire: "30",
    forecastTire: "40",
    actualRevenue: "360,000,000"
  },
  {
    id: "rev-13",
    customer: "RIUNG MITRA LESTARI",
    tireSize: "24.00R35",
    customerTotalTire: "36",
    forecastTire: "48",
    actualRevenue: "432,000,000"
  },
  {
    id: "rev-14",
    customer: "MADHANI TALATAH NUSANTARA",
    tireSize: "27.00R49",
    customerTotalTire: "54",
    forecastTire: "72",
    actualRevenue: "648,000,000"
  },
  {
    id: "rev-15",
    customer: "PETROSEA",
    tireSize: "11.00R20",
    customerTotalTire: "72",
    forecastTire: "96",
    actualRevenue: "144,000,000"
  },
  {
    id: "rev-16",
    customer: "RIUNG MITRA LESTARI",
    tireSize: "12.00R24",
    customerTotalTire: "54",
    forecastTire: "72",
    actualRevenue: "135,000,000"
  }
];
