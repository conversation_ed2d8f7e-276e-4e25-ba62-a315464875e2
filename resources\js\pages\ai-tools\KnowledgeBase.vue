<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">AI Knowledge Base</h1>
                    <p class="mt-2 text-gray-600">Intelligent knowledge management system with AI-powered search and insights</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="addKnowledge"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                    >
                        <Plus class="h-4 w-4 mr-2" />
                        Add Knowledge
                    </button>
                    <button
                        @click="exportKnowledge"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export
                    </button>
                </div>
            </div>

            <!-- Search & AI Assistant -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center space-x-4 mb-6">
                    <div class="flex-1">
                        <div class="relative">
                            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                            <input
                                v-model="searchQuery"
                                type="text"
                                placeholder="Ask AI anything about tires, mining, or business processes..."
                                class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                @keyup.enter="performAISearch"
                            />
                        </div>
                    </div>
                    <button
                        @click="performAISearch"
                        :disabled="!searchQuery.trim()"
                        class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                        <Brain class="h-5 w-5 mr-2" />
                        Ask AI
                    </button>
                </div>

                <!-- AI Response -->
                <div v-if="aiResponse" class="mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg">
                    <div class="flex items-start">
                        <Brain class="h-6 w-6 text-purple-600 mt-1 mr-3 flex-shrink-0" />
                        <div class="flex-1">
                            <h4 class="font-medium text-purple-900 mb-2">AI Assistant Response</h4>
                            <p class="text-purple-800 mb-3">{{ aiResponse.answer }}</p>
                            <div class="flex items-center space-x-4 text-sm">
                                <span class="text-purple-600">Confidence: {{ aiResponse.confidence }}%</span>
                                <span class="text-purple-600">Sources: {{ aiResponse.sources }} articles</span>
                                <button class="text-purple-700 hover:text-purple-900 underline">View Sources</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Filters -->
                <div class="flex flex-wrap gap-2">
                    <button
                        v-for="filter in quickFilters"
                        :key="filter"
                        @click="applyFilter(filter)"
                        :class="[
                            'px-3 py-1 text-sm rounded-full border transition-colors',
                            selectedFilter === filter
                                ? 'bg-blue-100 text-blue-800 border-blue-300'
                                : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                        ]"
                    >
                        {{ filter }}
                    </button>
                </div>
            </div>

            <!-- Knowledge Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <BookOpen class="h-8 w-8 text-blue-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Articles</p>
                            <p class="text-2xl font-bold text-gray-900">{{ knowledgeStats.totalArticles.toLocaleString() }}</p>
                            <p class="text-sm text-blue-600">{{ knowledgeStats.newThisMonth }} added this month</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Search class="h-8 w-8 text-green-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">AI Queries</p>
                            <p class="text-2xl font-bold text-gray-900">{{ knowledgeStats.aiQueries.toLocaleString() }}</p>
                            <p class="text-sm text-green-600">{{ knowledgeStats.avgAccuracy }}% accuracy</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Users class="h-8 w-8 text-purple-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Active Users</p>
                            <p class="text-2xl font-bold text-gray-900">{{ knowledgeStats.activeUsers }}</p>
                            <p class="text-sm text-purple-600">{{ knowledgeStats.userGrowth }}% growth</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <TrendingUp class="h-8 w-8 text-orange-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Knowledge Score</p>
                            <p class="text-2xl font-bold text-gray-900">{{ knowledgeStats.knowledgeScore }}/100</p>
                            <p class="text-sm text-orange-600">{{ knowledgeStats.scoreChange }}% improvement</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Knowledge Categories -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Categories -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Knowledge Categories</h3>
                    <div class="space-y-4">
                        <div v-for="category in knowledgeCategories" :key="category.name" class="flex items-center justify-between">
                            <div class="flex items-center">
                                <component :is="category.icon" class="h-5 w-5 text-gray-600 mr-3" />
                                <div>
                                    <h4 class="font-medium text-gray-900">{{ category.name }}</h4>
                                    <p class="text-sm text-gray-600">{{ category.description }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-gray-900">{{ category.articles }}</p>
                                <p class="text-sm text-gray-500">articles</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                    <div class="space-y-4">
                        <div v-for="activity in recentActivity" :key="activity.id" class="flex items-start space-x-3">
                            <div :class="['w-8 h-8 rounded-full flex items-center justify-center', activity.color]">
                                <component :is="activity.icon" class="h-4 w-4 text-white" />
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">{{ activity.title }}</p>
                                <p class="text-sm text-gray-600">{{ activity.description }}</p>
                                <p class="text-xs text-gray-500 mt-1">{{ activity.time }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Knowledge Articles -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Knowledge Articles</h3>
                    <div class="flex space-x-3">
                        <select v-model="sortBy" class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <option value="recent">Most Recent</option>
                            <option value="popular">Most Popular</option>
                            <option value="relevant">Most Relevant</option>
                        </select>
                        <button class="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center text-sm">
                            <Filter class="h-4 w-4 mr-2" />
                            Filter
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div v-for="article in filteredArticles" :key="article.id" class="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                            <div class="flex-shrink-0">
                                <div :class="['w-12 h-12 rounded-lg flex items-center justify-center', getCategoryColor(article.category)]">
                                    <component :is="getCategoryIcon(article.category)" class="h-6 w-6 text-white" />
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h4 class="font-medium text-gray-900 truncate">{{ article.title }}</h4>
                                    <span class="text-sm text-gray-500">{{ article.updatedAt }}</span>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">{{ article.summary }}</p>
                                <div class="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <Eye class="h-4 w-4 mr-1" />
                                        {{ article.views }}
                                    </div>
                                    <div class="flex items-center">
                                        <ThumbsUp class="h-4 w-4 mr-1" />
                                        {{ article.likes }}
                                    </div>
                                    <div class="flex items-center">
                                        <MessageSquare class="h-4 w-4 mr-1" />
                                        {{ article.comments }}
                                    </div>
                                    <span class="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">{{ article.category }}</span>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <button class="text-blue-600 hover:text-blue-800 text-sm">View Article</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Insights -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">AI Knowledge Insights</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">Content Gaps</h4>
                        <div v-for="gap in contentGaps" :key="gap.id" class="flex items-start p-3 bg-yellow-50 rounded-lg">
                            <AlertTriangle class="h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div>
                                <p class="text-sm font-medium text-yellow-900">{{ gap.topic }}</p>
                                <p class="text-sm text-yellow-700">{{ gap.description }}</p>
                                <p class="text-xs text-yellow-600 mt-1">{{ gap.queries }} related queries</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">Popular Topics</h4>
                        <div v-for="topic in popularTopics" :key="topic.id" class="flex items-start p-3 bg-green-50 rounded-lg">
                            <TrendingUp class="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div>
                                <p class="text-sm font-medium text-green-900">{{ topic.name }}</p>
                                <p class="text-sm text-green-700">{{ topic.description }}</p>
                                <p class="text-xs text-green-600 mt-1">{{ topic.searches }} searches this month</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-purple-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-purple-900">Advanced AI Knowledge Features Coming Soon</h3>
                        <p class="text-purple-700 mt-1">
                            Enhanced knowledge management features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-purple-700 mt-2 space-y-1">
                            <li>Advanced natural language processing for better search</li>
                            <li>Automatic content generation from documents</li>
                            <li>Multi-language knowledge base support</li>
                            <li>Integration with external knowledge sources</li>
                            <li>Collaborative editing and version control</li>
                            <li>AI-powered content recommendations</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Plus,
    Download,
    Search,
    Brain,
    BookOpen,
    Users,
    TrendingUp,
    Filter,
    Eye,
    ThumbsUp,
    MessageSquare,
    AlertTriangle,
    Settings,
    FileText,
    Wrench,
    Truck,
    Info
} from 'lucide-vue-next';
import { ref, computed, onMounted } from 'vue';

// Reactive state
const searchQuery = ref('');
const selectedFilter = ref('All');
const sortBy = ref('recent');
const aiResponse = ref(null);

// Quick filters
const quickFilters = ['All', 'Tire Technology', 'Mining Equipment', 'Maintenance', 'Safety', 'Business Process'];

// Knowledge statistics
const knowledgeStats = ref({
    totalArticles: 1247,
    newThisMonth: 23,
    aiQueries: 3456,
    avgAccuracy: 94,
    activeUsers: 89,
    userGrowth: 15,
    knowledgeScore: 87,
    scoreChange: 12
});

// Knowledge categories
const knowledgeCategories = ref([
    { name: 'Tire Technology', description: 'Technical specifications and innovations', articles: 234, icon: Settings },
    { name: 'Mining Equipment', description: 'Heavy equipment and machinery', articles: 189, icon: Truck },
    { name: 'Maintenance Procedures', description: 'Step-by-step maintenance guides', articles: 156, icon: Wrench },
    { name: 'Safety Protocols', description: 'Safety guidelines and procedures', articles: 123, icon: AlertTriangle },
    { name: 'Business Process', description: 'Company procedures and workflows', articles: 98, icon: FileText }
]);

// Recent activity
const recentActivity = ref([
    {
        id: 1,
        title: 'New article added',
        description: 'Tire Pressure Monitoring Systems for Mining Vehicles',
        time: '2 hours ago',
        icon: Plus,
        color: 'bg-green-500'
    },
    {
        id: 2,
        title: 'AI query answered',
        description: 'How to optimize tire life in extreme conditions?',
        time: '4 hours ago',
        icon: Brain,
        color: 'bg-purple-500'
    },
    {
        id: 3,
        title: 'Article updated',
        description: 'Safety protocols for tire installation',
        time: '6 hours ago',
        icon: FileText,
        color: 'bg-blue-500'
    }
]);

// Knowledge articles
const articles = ref([
    {
        id: 1,
        title: 'Tire Pressure Monitoring for Heavy Equipment',
        summary: 'Complete guide on implementing TPMS for mining and construction vehicles',
        category: 'Tire Technology',
        views: 1234,
        likes: 89,
        comments: 23,
        updatedAt: '2 days ago'
    },
    {
        id: 2,
        title: 'Preventive Maintenance Schedule for Mining Tires',
        summary: 'Optimal maintenance intervals and procedures to maximize tire lifespan',
        category: 'Maintenance',
        views: 987,
        likes: 67,
        comments: 15,
        updatedAt: '3 days ago'
    },
    {
        id: 3,
        title: 'Safety Protocols for Tire Installation',
        summary: 'Step-by-step safety procedures for installing heavy equipment tires',
        category: 'Safety',
        views: 756,
        likes: 45,
        comments: 12,
        updatedAt: '5 days ago'
    },
    {
        id: 4,
        title: 'Understanding Tire Load Ratings',
        summary: 'Comprehensive guide to tire load ratings and their applications',
        category: 'Tire Technology',
        views: 654,
        likes: 34,
        comments: 8,
        updatedAt: '1 week ago'
    }
]);

// Content gaps
const contentGaps = ref([
    {
        id: 1,
        topic: 'Electric Vehicle Tires',
        description: 'Limited content on tires for electric mining vehicles',
        queries: 45
    },
    {
        id: 2,
        topic: 'Tire Recycling Process',
        description: 'Need more detailed recycling and disposal procedures',
        queries: 32
    },
    {
        id: 3,
        topic: 'Cold Weather Performance',
        description: 'Insufficient information on tire performance in extreme cold',
        queries: 28
    }
]);

// Popular topics
const popularTopics = ref([
    {
        id: 1,
        name: 'Tire Maintenance',
        description: 'Most searched topic this month',
        searches: 456
    },
    {
        id: 2,
        name: 'Load Capacity',
        description: 'High interest in load rating information',
        searches: 389
    },
    {
        id: 3,
        name: 'Safety Procedures',
        description: 'Growing interest in safety protocols',
        searches: 234
    }
]);

// Computed properties
const filteredArticles = computed(() => {
    let filtered = articles.value;

    if (selectedFilter.value !== 'All') {
        filtered = filtered.filter(article =>
            article.category.toLowerCase().includes(selectedFilter.value.toLowerCase())
        );
    }

    if (searchQuery.value) {
        filtered = filtered.filter(article =>
            article.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            article.summary.toLowerCase().includes(searchQuery.value.toLowerCase())
        );
    }

    // Sort articles
    switch (sortBy.value) {
        case 'popular':
            return filtered.sort((a, b) => b.views - a.views);
        case 'relevant':
            return filtered.sort((a, b) => b.likes - a.likes);
        default:
            return filtered;
    }
});

// Utility functions
const getCategoryColor = (category: string): string => {
    const colors: Record<string, string> = {
        'Tire Technology': 'bg-blue-500',
        'Maintenance': 'bg-green-500',
        'Safety': 'bg-red-500',
        'Mining Equipment': 'bg-yellow-500',
        'Business Process': 'bg-purple-500'
    };
    return colors[category] || 'bg-gray-500';
};

const getCategoryIcon = (category: string) => {
    const icons: Record<string, any> = {
        'Tire Technology': Settings,
        'Maintenance': Wrench,
        'Safety': AlertTriangle,
        'Mining Equipment': Truck,
        'Business Process': FileText
    };
    return icons[category] || FileText;
};

// Event handlers
const performAISearch = async () => {
    if (!searchQuery.value.trim()) return;

    // Simulate AI processing
    aiResponse.value = null;

    setTimeout(() => {
        aiResponse.value = {
            answer: generateAIResponse(searchQuery.value),
            confidence: Math.floor(Math.random() * 20) + 80, // 80-100%
            sources: Math.floor(Math.random() * 5) + 3 // 3-8 sources
        };
    }, 1500);
};

const generateAIResponse = (query: string): string => {
    const responses: Record<string, string> = {
        'tire': 'Based on our knowledge base, tire selection depends on load requirements, operating conditions, and terrain. For mining applications, we recommend radial tires with reinforced sidewalls.',
        'maintenance': 'Regular tire maintenance includes pressure checks, visual inspections, and rotation schedules. For heavy equipment, inspect tires daily and maintain proper inflation pressure.',
        'safety': 'Safety protocols require proper training, use of safety equipment, and following lockout/tagout procedures during tire service. Never exceed recommended pressure limits.',
        'mining': 'Mining tire applications require specialized compounds for cut resistance and heat dissipation. Consider TKPH ratings and operating conditions when selecting tires.',
        'pressure': 'Proper tire pressure is critical for performance and safety. Check pressure when tires are cold and adjust according to load and operating conditions.'
    };

    const lowerQuery = query.toLowerCase();
    for (const [key, response] of Object.entries(responses)) {
        if (lowerQuery.includes(key)) {
            return response;
        }
    }

    return 'Based on our knowledge base analysis, I found relevant information about your query. Please check the suggested articles below for detailed information.';
};

const applyFilter = (filter: string) => {
    selectedFilter.value = filter;
};

const addKnowledge = () => {
    alert('Add Knowledge functionality coming soon!\n\nFeatures:\n- Rich text editor\n- Document upload\n- AI-assisted content creation\n- Category tagging\n- Version control');
};

const exportKnowledge = () => {
    try {
        const knowledgeData = {
            statistics: knowledgeStats.value,
            categories: knowledgeCategories.value,
            articles: filteredArticles.value,
            contentGaps: contentGaps.value,
            popularTopics: popularTopics.value,
            exportedAt: new Date().toISOString()
        };

        const csvContent = [
            ['Knowledge Base Export Report'].join(','),
            ['Generated:', new Date().toLocaleDateString('id-ID')].join(','),
            [],
            ['Statistics'].join(','),
            ['Total Articles', knowledgeStats.value.totalArticles].join(','),
            ['AI Queries', knowledgeStats.value.aiQueries].join(','),
            ['Active Users', knowledgeStats.value.activeUsers].join(','),
            ['Knowledge Score', `${knowledgeStats.value.knowledgeScore}/100`].join(','),
            [],
            ['Articles'].join(','),
            ['Title', 'Category', 'Views', 'Likes', 'Comments'].join(','),
            ...filteredArticles.value.map(article => [
                `"${article.title}"`,
                article.category,
                article.views,
                article.likes,
                article.comments
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `knowledge_base_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Knowledge base exported successfully!');
    } catch (error) {
        console.error('Error exporting knowledge base:', error);
        alert('Failed to export knowledge base.');
    }
};

// Initialize on mount
onMounted(() => {
    // Load knowledge base data
});
</script>
