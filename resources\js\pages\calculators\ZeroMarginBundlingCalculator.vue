<template>
    <AppLayout>
        <div class="space-y-6">
            <div class="border-b border-gray-200 pb-4">
                <h1 class="text-2xl font-bold text-gray-900">27.00R49 0% + Bonus Calculator</h1>
                <p class="mt-2 text-gray-600">Calculate zero margin bundling with bonus products.</p>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="text-center py-12">
                    <div class="mx-auto h-12 w-12 text-gray-400">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
                        </svg>
                    </div>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Calculator Coming Soon</h3>
                    <p class="mt-1 text-sm text-gray-500">This calculator will help you create zero margin bundles with bonus products.</p>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
</script>
