<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">AI Proposal Analyzer</h1>
                    <p class="mt-2 text-gray-600">Intelligent analysis of business proposals with AI-powered insights and optimization</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="uploadProposal"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                    >
                        <Upload class="h-4 w-4 mr-2" />
                        Upload Proposal
                    </button>
                    <button
                        @click="exportAnalysis"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export Analysis
                    </button>
                </div>
            </div>

            <!-- Upload Section -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Upload Proposal Document</h3>
                
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <FileText class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h4 class="text-lg font-medium text-gray-900 mb-2">Drop your proposal document here</h4>
                    <p class="text-gray-600 mb-4">
                        Supports PDF, DOCX, and TXT files up to 10MB
                    </p>
                    <div class="flex justify-center space-x-4">
                        <button
                            @click="selectFile"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                        >
                            Select File
                        </button>
                        <button
                            @click="showDemo"
                            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                        >
                            View Demo Analysis
                        </button>
                    </div>
                </div>

                <div class="mt-4 text-sm text-gray-500">
                    <p><strong>What we analyze:</strong></p>
                    <ul class="list-disc list-inside mt-2 space-y-1">
                        <li>Proposal structure and completeness</li>
                        <li>Pricing strategy and competitiveness</li>
                        <li>Technical specifications accuracy</li>
                        <li>Risk assessment and mitigation</li>
                        <li>Compliance with requirements</li>
                    </ul>
                </div>
            </div>

            <!-- Analysis Results -->
            <div v-if="analysisResults" class="space-y-6">
                <!-- Overall Score -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Overall Proposal Score</h3>
                        <div class="flex items-center space-x-4">
                            <div class="text-right">
                                <p class="text-3xl font-bold text-blue-600">{{ analysisResults.overallScore }}/100</p>
                                <p class="text-sm text-gray-600">{{ analysisResults.scoreCategory }}</p>
                            </div>
                            <div class="w-16 h-16 relative">
                                <svg class="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                                    <path
                                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                        fill="none"
                                        stroke="#e5e7eb"
                                        stroke-width="3"
                                    />
                                    <path
                                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                        fill="none"
                                        stroke="#3b82f6"
                                        stroke-width="3"
                                        :stroke-dasharray="`${analysisResults.overallScore}, 100`"
                                    />
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Scores -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div v-for="metric in analysisResults.detailedScores" :key="metric.name" class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">{{ metric.name }}</p>
                                <p class="text-2xl font-bold text-gray-900">{{ metric.score }}/100</p>
                                <p :class="[
                                    'text-sm',
                                    metric.score >= 80 ? 'text-green-600' :
                                    metric.score >= 60 ? 'text-yellow-600' :
                                    'text-red-600'
                                ]">
                                    {{ metric.status }}
                                </p>
                            </div>
                            <component :is="metric.icon" :class="[
                                'h-8 w-8',
                                metric.score >= 80 ? 'text-green-600' :
                                metric.score >= 60 ? 'text-yellow-600' :
                                'text-red-600'
                            ]" />
                        </div>
                    </div>
                </div>

                <!-- Strengths and Weaknesses -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Strengths -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Strengths</h3>
                        <div class="space-y-3">
                            <div v-for="strength in analysisResults.strengths" :key="strength.id" class="flex items-start p-3 bg-green-50 rounded-lg">
                                <CheckCircle class="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                                <div>
                                    <p class="text-sm font-medium text-green-900">{{ strength.title }}</p>
                                    <p class="text-sm text-green-700">{{ strength.description }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Weaknesses -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Areas for Improvement</h3>
                        <div class="space-y-3">
                            <div v-for="weakness in analysisResults.weaknesses" :key="weakness.id" class="flex items-start p-3 bg-red-50 rounded-lg">
                                <AlertTriangle class="h-5 w-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
                                <div>
                                    <p class="text-sm font-medium text-red-900">{{ weakness.title }}</p>
                                    <p class="text-sm text-red-700">{{ weakness.description }}</p>
                                    <p class="text-xs text-red-600 mt-1">Impact: {{ weakness.impact }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recommendations -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">AI Recommendations</h3>
                    <div class="space-y-4">
                        <div v-for="recommendation in analysisResults.recommendations" :key="recommendation.id" class="flex items-start p-4 border border-gray-200 rounded-lg">
                            <Lightbulb class="h-6 w-6 text-blue-600 mt-1 mr-4 flex-shrink-0" />
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-medium text-gray-900">{{ recommendation.title }}</h4>
                                    <span :class="[
                                        'px-2 py-1 text-xs font-medium rounded-full',
                                        recommendation.priority === 'High' ? 'bg-red-100 text-red-800' :
                                        recommendation.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-green-100 text-green-800'
                                    ]">
                                        {{ recommendation.priority }} Priority
                                    </span>
                                </div>
                                <p class="text-sm text-gray-700 mb-2">{{ recommendation.description }}</p>
                                <p class="text-xs text-blue-600">Expected improvement: {{ recommendation.improvement }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Competitive Analysis -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Competitive Positioning</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <TrendingUp class="h-8 w-8 text-blue-600 mx-auto mb-2" />
                            <h4 class="font-medium text-blue-900">Price Competitiveness</h4>
                            <p class="text-2xl font-bold text-blue-900">{{ analysisResults.competitive.priceScore }}%</p>
                            <p class="text-sm text-blue-700">{{ analysisResults.competitive.pricePosition }}</p>
                        </div>
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <Star class="h-8 w-8 text-green-600 mx-auto mb-2" />
                            <h4 class="font-medium text-green-900">Technical Advantage</h4>
                            <p class="text-2xl font-bold text-green-900">{{ analysisResults.competitive.techScore }}%</p>
                            <p class="text-sm text-green-700">{{ analysisResults.competitive.techPosition }}</p>
                        </div>
                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <Award class="h-8 w-8 text-purple-600 mx-auto mb-2" />
                            <h4 class="font-medium text-purple-900">Win Probability</h4>
                            <p class="text-2xl font-bold text-purple-900">{{ analysisResults.competitive.winProbability }}%</p>
                            <p class="text-sm text-purple-700">{{ analysisResults.competitive.winCategory }}</p>
                        </div>
                    </div>
                </div>

                <!-- Risk Assessment -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Risk Assessment</h3>
                    <div class="space-y-4">
                        <div v-for="risk in analysisResults.risks" :key="risk.id" class="flex items-start p-3 rounded-lg"
                             :class="risk.level === 'High' ? 'bg-red-50' : risk.level === 'Medium' ? 'bg-yellow-50' : 'bg-green-50'">
                            <AlertTriangle v-if="risk.level === 'High'" class="h-5 w-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
                            <AlertCircle v-else-if="risk.level === 'Medium'" class="h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
                            <CheckCircle v-else class="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <h4 class="font-medium" :class="risk.level === 'High' ? 'text-red-900' : risk.level === 'Medium' ? 'text-yellow-900' : 'text-green-900'">
                                        {{ risk.title }}
                                    </h4>
                                    <span :class="[
                                        'px-2 py-1 text-xs font-medium rounded-full',
                                        risk.level === 'High' ? 'bg-red-100 text-red-800' :
                                        risk.level === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-green-100 text-green-800'
                                    ]">
                                        {{ risk.level }} Risk
                                    </span>
                                </div>
                                <p class="text-sm mt-1" :class="risk.level === 'High' ? 'text-red-700' : risk.level === 'Medium' ? 'text-yellow-700' : 'text-green-700'">
                                    {{ risk.description }}
                                </p>
                                <p class="text-xs mt-1" :class="risk.level === 'High' ? 'text-red-600' : risk.level === 'Medium' ? 'text-yellow-600' : 'text-green-600'">
                                    Mitigation: {{ risk.mitigation }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-blue-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-blue-900">Advanced Proposal Analysis Coming Soon</h3>
                        <p class="text-blue-700 mt-1">
                            Enhanced proposal analysis features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-blue-700 mt-2 space-y-1">
                            <li>Real-time collaboration and commenting</li>
                            <li>Integration with CRM and project management tools</li>
                            <li>Automated compliance checking</li>
                            <li>Multi-language proposal analysis</li>
                            <li>Historical win/loss analysis</li>
                            <li>Automated proposal generation from templates</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Upload,
    Download,
    FileText,
    CheckCircle,
    AlertTriangle,
    AlertCircle,
    Lightbulb,
    TrendingUp,
    Star,
    Award,
    Target,
    DollarSign,
    Shield,
    Clock,
    Info
} from 'lucide-vue-next';
import { ref, onMounted } from 'vue';

// Reactive state
const analysisResults = ref(null);

// Demo analysis data
const demoAnalysis = {
    overallScore: 78,
    scoreCategory: 'Good',
    detailedScores: [
        { name: 'Structure', score: 85, status: 'Excellent', icon: FileText },
        { name: 'Pricing', score: 72, status: 'Good', icon: DollarSign },
        { name: 'Technical', score: 80, status: 'Very Good', icon: Target },
        { name: 'Compliance', score: 75, status: 'Good', icon: Shield }
    ],
    strengths: [
        {
            id: 1,
            title: 'Comprehensive Technical Specifications',
            description: 'Detailed technical requirements and specifications are well-documented'
        },
        {
            id: 2,
            title: 'Clear Project Timeline',
            description: 'Well-defined milestones and delivery schedule with realistic timeframes'
        },
        {
            id: 3,
            title: 'Strong Value Proposition',
            description: 'Clear articulation of benefits and return on investment'
        }
    ],
    weaknesses: [
        {
            id: 1,
            title: 'Limited Risk Mitigation',
            description: 'Risk assessment section lacks detailed mitigation strategies',
            impact: 'Medium'
        },
        {
            id: 2,
            title: 'Pricing Justification',
            description: 'Pricing breakdown could be more detailed and transparent',
            impact: 'High'
        },
        {
            id: 3,
            title: 'Competitive Analysis',
            description: 'Missing comparison with competitor offerings',
            impact: 'Medium'
        }
    ],
    recommendations: [
        {
            id: 1,
            title: 'Enhance Risk Management Section',
            description: 'Add detailed risk assessment matrix with specific mitigation strategies for each identified risk',
            priority: 'High',
            improvement: '15% score increase'
        },
        {
            id: 2,
            title: 'Improve Pricing Transparency',
            description: 'Provide detailed cost breakdown and justification for each line item',
            priority: 'High',
            improvement: '12% score increase'
        },
        {
            id: 3,
            title: 'Add Competitive Differentiation',
            description: 'Include section highlighting unique advantages over competitors',
            priority: 'Medium',
            improvement: '8% score increase'
        },
        {
            id: 4,
            title: 'Strengthen Implementation Plan',
            description: 'Add more detailed project phases and resource allocation',
            priority: 'Medium',
            improvement: '6% score increase'
        }
    ],
    competitive: {
        priceScore: 68,
        pricePosition: 'Competitive',
        techScore: 85,
        techPosition: 'Strong Advantage',
        winProbability: 72,
        winCategory: 'High Chance'
    },
    risks: [
        {
            id: 1,
            title: 'Delivery Timeline Risk',
            description: 'Aggressive timeline may impact quality or require additional resources',
            level: 'Medium',
            mitigation: 'Add buffer time and resource contingency'
        },
        {
            id: 2,
            title: 'Technical Complexity',
            description: 'Some technical requirements may be challenging to implement',
            level: 'Medium',
            mitigation: 'Conduct technical feasibility study'
        },
        {
            id: 3,
            title: 'Price Sensitivity',
            description: 'Pricing may be above client budget expectations',
            level: 'High',
            mitigation: 'Provide alternative pricing options'
        },
        {
            id: 4,
            title: 'Regulatory Compliance',
            description: 'All regulatory requirements are addressed',
            level: 'Low',
            mitigation: 'Regular compliance reviews'
        }
    ]
};

// Event handlers
const uploadProposal = () => {
    alert('Proposal upload functionality coming soon!\n\nFeatures:\n- Drag & drop document upload\n- Support for PDF, DOCX, TXT formats\n- Real-time analysis progress\n- Automatic text extraction and parsing');
};

const selectFile = () => {
    // Create file input element
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.pdf,.docx,.txt';
    input.onchange = (event) => {
        const file = (event.target as HTMLInputElement).files?.[0];
        if (file) {
            alert(`File selected: ${file.name}\n\nAnalyzing proposal...\n\nThis will analyze:\n- Document structure\n- Pricing strategy\n- Technical specifications\n- Risk assessment\n- Competitive positioning`);
            // Show demo analysis after "processing"
            setTimeout(() => {
                analysisResults.value = demoAnalysis;
            }, 3000);
        }
    };
    input.click();
};

const showDemo = () => {
    analysisResults.value = demoAnalysis;
    alert('Demo analysis loaded!\n\nThis shows sample analysis of a business proposal for heavy equipment tire supply contract.');
};

const exportAnalysis = () => {
    if (!analysisResults.value) {
        alert('No analysis to export. Please upload a proposal first.');
        return;
    }

    try {
        const reportData = {
            overallScore: analysisResults.value.overallScore,
            detailedScores: analysisResults.value.detailedScores,
            strengths: analysisResults.value.strengths,
            weaknesses: analysisResults.value.weaknesses,
            recommendations: analysisResults.value.recommendations,
            competitive: analysisResults.value.competitive,
            risks: analysisResults.value.risks,
            generatedAt: new Date().toISOString()
        };

        const csvContent = [
            ['Proposal Analysis Report'].join(','),
            ['Generated:', new Date().toLocaleDateString('id-ID')].join(','),
            ['Overall Score:', `${analysisResults.value.overallScore}/100`].join(','),
            [],
            ['Detailed Scores'].join(','),
            ['Category', 'Score', 'Status'].join(','),
            ...analysisResults.value.detailedScores.map(score => [score.name, `${score.score}/100`, score.status].join(',')),
            [],
            ['Strengths'].join(','),
            ['Title', 'Description'].join(','),
            ...analysisResults.value.strengths.map(strength => [`"${strength.title}"`, `"${strength.description}"`].join(',')),
            [],
            ['Areas for Improvement'].join(','),
            ['Title', 'Description', 'Impact'].join(','),
            ...analysisResults.value.weaknesses.map(weakness => [`"${weakness.title}"`, `"${weakness.description}"`, weakness.impact].join(',')),
            [],
            ['Recommendations'].join(','),
            ['Title', 'Description', 'Priority', 'Expected Improvement'].join(','),
            ...analysisResults.value.recommendations.map(rec => [
                `"${rec.title}"`,
                `"${rec.description}"`,
                rec.priority,
                rec.improvement
            ].join(',')),
            [],
            ['Risk Assessment'].join(','),
            ['Title', 'Description', 'Level', 'Mitigation'].join(','),
            ...analysisResults.value.risks.map(risk => [
                `"${risk.title}"`,
                `"${risk.description}"`,
                risk.level,
                `"${risk.mitigation}"`
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `proposal_analysis_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Proposal analysis exported successfully!');
    } catch (error) {
        console.error('Error exporting analysis:', error);
        alert('Failed to export analysis.');
    }
};

// Initialize on mount
onMounted(() => {
    // Load any saved analysis
});
</script>
