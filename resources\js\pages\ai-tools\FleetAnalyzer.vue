<!-- ...bagian atas file tidak berubah... -->
        <!-- Vehicle Performance Analysis Table -->
        <div class="bg-white rounded-lg shadow-sm border overflow-x-auto" style="max-width: 100%">
          <h3 class="text-lg font-semibold p-4 pb-0">Vehicle Performance Analysis</h3>
          <table class="min-w-full divide-y divide-gray-200 table-fixed">
            <thead>
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">VEHICLE ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">TYPE</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">UTILIZATION</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">FUEL EFFICIENCY</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">MAINTENANCE COST</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">TIRE CONDITION</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">STATUS</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="item in filteredData" :key="item.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 text-sm text-gray-900 font-semibold">{{ item.vehicle_id || '-' }}</td>
                <td class="px-6 py-4 text-sm text-gray-500">{{ item.type || '-' }}</td>
                <td class="px-6 py-4 text-sm">
                  <span class="inline-block w-24 h-2 bg-blue-100 rounded-full mr-2 align-middle">
                    <span
                      class="block h-2 bg-blue-600 rounded-full"
                      :style="{ width: (parseFloat(item.utilization) || 0) + '%' }"
                    ></span>
                  </span>
                  <span class="align-middle">{{ item.utilization || '-' }}</span>
                </td>
                <td class="px-6 py-4 text-sm text-gray-500">{{ item.fuel_efficiency || '-' }}</td>
                <td class="px-6 py-4 text-sm text-gray-500">{{ item.maintenance_cost || '-' }}</td>
                <td class="px-6 py-4 text-sm">
                  <span
                    v-if="item.tire_condition"
                    :class="{
                      'bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs': item.tire_condition.toLowerCase() === 'good',
                      'bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs': item.tire_condition.toLowerCase() === 'fair',
                      'bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs': item.tire_condition.toLowerCase() === 'bad'
                    }"
                  >
                    {{ item.tire_condition }}
                  </span>
                  <span v-else>-</span>
                </td>
                <td class="px-6 py-4 text-sm">
                  <span
                    v-if="item.status"
                    :class="{
                      'bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs': item.status.toLowerCase() === 'active',
                      'bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs': item.status.toLowerCase() !== 'active'
                    }"
                  >
                    {{ item.status }}
                  </span>
                  <span v-else>-</span>
                </td>
              </tr>
            </tbody>
          </table>
          <div v-if="isLoading" class="p-8 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
            <p class="text-gray-600">Loading fleet data...</p>
          <div v-else-if="error" class="p-8 text-center">
            <p class="text-red-600">{{ error }}</p>
          </div>
          <div v-else-if="filteredData.length === 0" class="p-8 text-center">
            <p class="text-gray-600">No fleet data found matching your criteria.</p>
          </div>
<!-- ...bagian bawah file tidak berubah... -->