<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-900 flex items-center">
        <span class="icon"><!-- Truck Icon Placeholder --></span>
        Fleet Data Analyzer
      </h1>
      <button
        @click="loadFleetData"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <span class="icon"><!-- Refresh Icon Placeholder --></span>
        Refresh Data
      </button>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white rounded-lg shadow-sm border p-4">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1 relative">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Cari data armada..."
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
      </div>
    </div>

    <div class="flex gap-6">
      <!-- Filter Sidebar -->
      <div class="w-72 bg-white rounded-lg shadow-sm border p-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold flex items-center">
            <span class="icon"><!-- Filter Icon Placeholder --></span>
            Filters
          </h3>
          <button
            @click="resetFilters"
            class="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded flex items-center"
          >
            <span class="icon"><!-- Refresh Icon Placeholder --></span>
            Reset
          </button>
        </div>
        <div class="space-y-4">
          <!-- Customer Filter -->
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Customer Name</label>
            <select multiple v-model="customerFilter" class="w-full border rounded p-1 text-xs">
              <option v-for="c in uniqueCustomers" :key="c" :value="c">{{ c }}</option>
            </select>
          </div>
          <!-- Status Filter -->
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Status</label>
            <select multiple v-model="statusFilter" class="w-full border rounded p-1 text-xs">
              <option v-for="s in uniqueStatuses" :key="s" :value="s">{{ s }}</option>
            </select>
          </div>
          <!-- Location Filter -->
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Location</label>
            <select multiple v-model="locationFilter" class="w-full border rounded p-1 text-xs">
              <option v-for="l in uniqueLocations" :key="l" :value="l">{{ l }}</option>
            </select>
          </div>
          <!-- Tire Size Filter -->
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Tire Size</label>
            <select multiple v-model="tireSizeFilter" class="w-full border rounded p-1 text-xs">
              <option v-for="t in uniqueTireSizes" :key="t" :value="t">{{ t }}</option>
            </select>
          </div>
          <!-- Model Filter -->
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Model</label>
            <select multiple v-model="modelFilter" class="w-full border rounded p-1 text-xs">
              <option v-for="m in uniqueModels" :key="m" :value="m">{{ m }}</option>
            </select>
          </div>
          <!-- Unit Manufacture Filter -->
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Unit Manufacture</label>
            <select multiple v-model="unitManufactureFilter" class="w-full border rounded p-1 text-xs">
              <option v-for="u in uniqueUnitManufactures" :key="u" :value="u">{{ u }}</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1">
        <!-- Scorecards Placeholder -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div class="bg-white rounded-lg shadow-sm border p-4">
const tableColumns = computed(() => {
  if (!filteredData.value.length) return [];
  return Object.keys(filteredData.value[0]).filter(key => key !== 'id');
});
            <div class="flex items-center">
              <span class="icon"><!-- Users Icon Placeholder --></span>
              <h3 class="text-sm font-medium text-gray-500">Total Customers</h3>
            </div>
            <p class="mt-2 text-2xl font-semibold text-gray-900">{{ scorecardMetrics.totalCustomers }}</p>
          </div>
          <div class="bg-white rounded-lg shadow-sm border p-4">
            <div class="flex items-center">
              <span class="icon"><!-- Truck Icon Placeholder --></span>
              <h3 class="text-sm font-medium text-gray-500">Total Units</h3>
            </div>
            <p class="mt-2 text-2xl font-semibold text-gray-900">{{ scorecardMetrics.totalUnits }}</p>
          </div>
          <div class="bg-white rounded-lg shadow-sm border p-4">
            <div class="flex items-center">
              <span class="icon"><!-- Disc Icon Placeholder --></span>
              <h3 class="text-sm font-medium text-gray-500">Total Tires</h3>
            </div>
            <p class="mt-2 text-2xl font-semibold text-gray-900">{{ scorecardMetrics.totalTires }}</p>
          </div>
          <div class="bg-white rounded-lg shadow-sm border p-4">
            <div class="flex items-center">
              <span class="icon"><!-- Activity Icon Placeholder --></span>
              <h3 class="text-sm font-medium text-gray-500">Active Units</h3>
            </div>
            <p class="mt-2 text-2xl font-semibold text-gray-900">{{ scorecardMetrics.activeUnits }}</p>
          </div>
        </div>

<!-- Data Table -->
        <div class="bg-white rounded-lg shadow-sm border overflow-x-auto" style="max-width: 100%">
          <table class="min-w-full divide-y divide-gray-200 table-fixed">
            <thead>
              <tr>
                <th v-for="col in tableColumns" :key="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
                  {{ col }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="item in filteredData" :key="item.id" class="hover:bg-gray-50">
                <td v-for="col in tableColumns" :key="col" class="px-6 py-4 text-sm text-gray-500" style="min-width: 150px; max-width: 300px; white-space: normal; word-break: break-word;">
                  {{ item[col] || '-' }}
                </td>
              </tr>
            </tbody>
          </table>
          <div v-if="isLoading" class="p-8 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
            <p class="text-gray-600">Loading fleet data...</p>
          </div>
          <div v-else-if="error" class="p-8 text-center">
            <p class="text-red-600">{{ error }}</p>
          </div>
          <div v-else-if="filteredData.length === 0" class="p-8 text-center">
            <p class="text-gray-600">No fleet data found matching your criteria.</p>
          </div>
        </div>
        <!-- Bar Chart Placeholder -->
        <div class="bg-white rounded-lg shadow-sm border p-4 mb-6">
          <h3 class="text-lg font-semibold mb-4 flex items-center">
            <span class="icon"><!-- BarChart2 Icon Placeholder --></span>
            Customer Analysis
          </h3>
          <div class="h-80">
            <!-- Chart.js Bar Chart will be implemented here -->
          </div>
        </div>

        <!-- Data Table Placeholder -->
        <div class="bg-white rounded-lg shadow-sm border overflow-hidden" style="max-width: 100%">
          <!-- Table, loading, error, paginasi akan diimplementasikan -->
        </div>
      </div>
    </div>

    <!-- FleetAIAnalysis Placeholder -->
    <div>
      <!-- Komponen analisis AI akan diimplementasikan di sini -->
    </div>
  </div>
</template>