<template>
    <AppLayout>
        <div class="space-y-6">
            <div class="border-b border-gray-200 pb-4">
                <h1 class="text-2xl font-bold text-gray-900">27.00 R 49 Bundling Calculator</h1>
                <p class="mt-2 text-gray-600">Calculate optimal bundling strategies for 27.00 R 49 tire products.</p>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="text-center py-12">
                    <div class="mx-auto h-12 w-12 text-gray-400">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Calculator Coming Soon</h3>
                    <p class="mt-1 text-sm text-gray-500">This calculator will help you optimize bundling strategies for tire products.</p>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
</script>
