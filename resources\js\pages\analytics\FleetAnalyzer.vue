<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Fleet Analyzer</h1>
                    <p class="mt-2 text-gray-600">Comprehensive fleet performance analysis and optimization insights</p>
                </div>
                <div class="flex space-x-3">
                    <select v-model="selectedFleet" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                        <option value="all">All Fleets</option>
                        <option value="mining">Mining Fleet</option>
                        <option value="construction">Construction Fleet</option>
                        <option value="logistics">Logistics Fleet</option>
                    </select>
                    <button
                        @click="generateReport"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                    >
                        <FileText class="h-4 w-4 mr-2" />
                        Generate Report
                    </button>
                    <button
                        @click="exportData"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export Data
                    </button>
                </div>
            </div>

            <!-- Fleet Overview KPIs -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Truck class="h-8 w-8 text-blue-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Vehicles</p>
                            <p class="text-2xl font-bold text-gray-900">{{ fleetKPIs.totalVehicles }}</p>
                            <p class="text-sm text-blue-600">{{ fleetKPIs.activeVehicles }} active</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Gauge class="h-8 w-8 text-green-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Fleet Utilization</p>
                            <p class="text-2xl font-bold text-gray-900">{{ fleetKPIs.utilization }}%</p>
                            <p class="text-sm text-green-600">+2.5% vs last month</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <DollarSign class="h-8 w-8 text-purple-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Operating Cost</p>
                            <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(fleetKPIs.operatingCost) }}</p>
                            <p class="text-sm text-purple-600">Per month</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <AlertTriangle class="h-8 w-8 text-orange-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Maintenance Due</p>
                            <p class="text-2xl font-bold text-gray-900">{{ fleetKPIs.maintenanceDue }}</p>
                            <p class="text-sm text-orange-600">Vehicles need service</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Utilization Trend -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Fleet Utilization Trend</h3>
                    <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                        <div class="text-center">
                            <BarChart3 class="h-12 w-12 text-gray-400 mx-auto mb-2" />
                            <p class="text-gray-600">Utilization Trend Chart</p>
                            <p class="text-sm text-gray-500">Chart.js integration</p>
                        </div>
                    </div>
                </div>

                <!-- Cost Breakdown -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Cost Breakdown</h3>
                    <div class="space-y-4">
                        <div v-for="cost in costBreakdown" :key="cost.category" class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div :class="['w-4 h-4 rounded-full mr-3', cost.color]"></div>
                                <span class="text-sm font-medium text-gray-900">{{ cost.category }}</span>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">{{ formatCurrency(cost.amount) }}</p>
                                <p class="text-xs text-gray-500">{{ cost.percentage }}%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fleet Performance Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Vehicle Performance Analysis</h3>
                    <div class="flex space-x-3">
                        <input
                            v-model="searchQuery"
                            type="text"
                            placeholder="Search vehicles..."
                            class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <button class="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center">
                            <Filter class="h-4 w-4 mr-2" />
                            Filter
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vehicle ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilization</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fuel Efficiency</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Maintenance Cost</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tire Condition</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="vehicle in filteredVehicles" :key="vehicle.id">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ vehicle.id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ vehicle.type }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                            <div 
                                                class="bg-blue-600 h-2 rounded-full" 
                                                :style="{ width: vehicle.utilization + '%' }"
                                            ></div>
                                        </div>
                                        <span class="text-sm text-gray-900">{{ vehicle.utilization }}%</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ vehicle.fuelEfficiency }} L/h</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatCurrency(vehicle.maintenanceCost) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="[
                                        'px-2 py-1 text-xs font-medium rounded-full',
                                        vehicle.tireCondition === 'Good' ? 'bg-green-100 text-green-800' :
                                        vehicle.tireCondition === 'Fair' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-red-100 text-red-800'
                                    ]">
                                        {{ vehicle.tireCondition }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="[
                                        'px-2 py-1 text-xs font-medium rounded-full',
                                        vehicle.status === 'Active' ? 'bg-green-100 text-green-800' :
                                        vehicle.status === 'Maintenance' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-red-100 text-red-800'
                                    ]">
                                        {{ vehicle.status }}
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Recommendations -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Optimization Recommendations</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">Immediate Actions</h4>
                        <div v-for="recommendation in immediateRecommendations" :key="recommendation.id" 
                             class="flex items-start p-3 bg-red-50 rounded-lg">
                            <AlertCircle class="h-5 w-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div>
                                <p class="text-sm font-medium text-red-900">{{ recommendation.title }}</p>
                                <p class="text-sm text-red-700">{{ recommendation.description }}</p>
                                <p class="text-xs text-red-600 mt-1">Potential savings: {{ recommendation.savings }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">Long-term Optimizations</h4>
                        <div v-for="optimization in longTermOptimizations" :key="optimization.id" 
                             class="flex items-start p-3 bg-blue-50 rounded-lg">
                            <Lightbulb class="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div>
                                <p class="text-sm font-medium text-blue-900">{{ optimization.title }}</p>
                                <p class="text-sm text-blue-700">{{ optimization.description }}</p>
                                <p class="text-xs text-blue-600 mt-1">ROI: {{ optimization.roi }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-green-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-green-900">Advanced Analytics Coming Soon</h3>
                        <p class="text-green-700 mt-1">
                            Enhanced fleet analytics features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-green-700 mt-2 space-y-1">
                            <li>Real-time GPS tracking and route optimization</li>
                            <li>Predictive maintenance using IoT sensors</li>
                            <li>AI-powered fuel consumption optimization</li>
                            <li>Driver behavior analysis and scoring</li>
                            <li>Environmental impact tracking</li>
                            <li>Integration with telematics systems</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    FileText,
    Download,
    Truck,
    Gauge,
    DollarSign,
    AlertTriangle,
    BarChart3,
    Filter,
    AlertCircle,
    Lightbulb,
    Info
} from 'lucide-vue-next';
import { ref, computed, onMounted } from 'vue';

// Reactive state
const selectedFleet = ref('all');
const searchQuery = ref('');

// Fleet KPIs
const fleetKPIs = ref({
    totalVehicles: 247,
    activeVehicles: 231,
    utilization: 78.5,
    operatingCost: 1850000000,
    maintenanceDue: 23
});

// Cost breakdown
const costBreakdown = ref([
    { category: 'Fuel', amount: 850000000, percentage: 46, color: 'bg-red-500' },
    { category: 'Maintenance', amount: 420000000, percentage: 23, color: 'bg-blue-500' },
    { category: 'Tires', amount: 310000000, percentage: 17, color: 'bg-green-500' },
    { category: 'Insurance', amount: 180000000, percentage: 10, color: 'bg-yellow-500' },
    { category: 'Other', amount: 90000000, percentage: 4, color: 'bg-purple-500' }
]);

// Vehicle data
const vehicles = ref([
    { id: 'HE-001', type: 'Excavator', utilization: 85, fuelEfficiency: 12.5, maintenanceCost: 2500000, tireCondition: 'Good', status: 'Active' },
    { id: 'HE-002', type: 'Dump Truck', utilization: 92, fuelEfficiency: 18.2, maintenanceCost: 3200000, tireCondition: 'Fair', status: 'Active' },
    { id: 'HE-003', type: 'Bulldozer', utilization: 76, fuelEfficiency: 15.8, maintenanceCost: 2800000, tireCondition: 'Good', status: 'Active' },
    { id: 'HE-004', type: 'Loader', utilization: 68, fuelEfficiency: 14.1, maintenanceCost: 1900000, tireCondition: 'Poor', status: 'Maintenance' },
    { id: 'HE-005', type: 'Grader', utilization: 82, fuelEfficiency: 16.5, maintenanceCost: 2100000, tireCondition: 'Good', status: 'Active' },
    { id: 'TR-001', type: 'Truck', utilization: 88, fuelEfficiency: 8.5, maintenanceCost: 1800000, tireCondition: 'Fair', status: 'Active' },
    { id: 'TR-002', type: 'Truck', utilization: 91, fuelEfficiency: 9.2, maintenanceCost: 2000000, tireCondition: 'Good', status: 'Active' },
    { id: 'TR-003', type: 'Truck', utilization: 45, fuelEfficiency: 7.8, maintenanceCost: 3500000, tireCondition: 'Poor', status: 'Maintenance' },
    { id: 'CN-001', type: 'Crane', utilization: 72, fuelEfficiency: 20.1, maintenanceCost: 4200000, tireCondition: 'Fair', status: 'Active' },
    { id: 'CN-002', type: 'Mixer', utilization: 79, fuelEfficiency: 11.8, maintenanceCost: 2600000, tireCondition: 'Good', status: 'Active' }
]);

// Recommendations
const immediateRecommendations = ref([
    {
        id: 1,
        title: 'Replace Tires on HE-004 and TR-003',
        description: 'Poor tire condition affecting fuel efficiency and safety',
        savings: 'Rp 15M/month'
    },
    {
        id: 2,
        title: 'Schedule Maintenance for 23 Vehicles',
        description: 'Overdue maintenance causing increased operating costs',
        savings: 'Rp 8M/month'
    },
    {
        id: 3,
        title: 'Optimize Route for Low Utilization Vehicles',
        description: 'TR-003 showing only 45% utilization',
        savings: 'Rp 5M/month'
    }
]);

const longTermOptimizations = ref([
    {
        id: 1,
        title: 'Implement Predictive Maintenance',
        description: 'Use IoT sensors to predict failures before they occur',
        roi: '250% in 18 months'
    },
    {
        id: 2,
        title: 'Fleet Right-sizing Analysis',
        description: 'Optimize fleet composition based on utilization patterns',
        roi: '180% in 12 months'
    },
    {
        id: 3,
        title: 'Driver Training Program',
        description: 'Improve fuel efficiency through better driving practices',
        roi: '150% in 6 months'
    }
]);

// Computed properties
const filteredVehicles = computed(() => {
    if (!searchQuery.value) return vehicles.value;
    return vehicles.value.filter(vehicle =>
        vehicle.id.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        vehicle.type.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
});

// Utility functions
const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

// Event handlers
const generateReport = () => {
    alert('Generating comprehensive fleet analysis report...');
};

const exportData = () => {
    try {
        const csvContent = [
            ['Vehicle ID', 'Type', 'Utilization (%)', 'Fuel Efficiency (L/h)', 'Maintenance Cost', 'Tire Condition', 'Status'].join(','),
            ...filteredVehicles.value.map(vehicle => [
                vehicle.id,
                vehicle.type,
                vehicle.utilization,
                vehicle.fuelEfficiency,
                vehicle.maintenanceCost,
                vehicle.tireCondition,
                vehicle.status
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `fleet_analysis_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Fleet data exported successfully!');
    } catch (error) {
        console.error('Error exporting data:', error);
        alert('Failed to export data.');
    }
};

// Initialize on mount
onMounted(() => {
    // Load fleet data
});
</script>
